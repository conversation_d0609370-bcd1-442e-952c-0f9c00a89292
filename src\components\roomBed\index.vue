<template>
	<div class="bed-arrangement-container">
		<!-- {{ orgCode }} -->
	  <div class="content">
		<!-- 左侧：未分配人员列表 -->
		<div class="unassigned-list">
		  <div class="sys-sub-title">待安排在押人员</div>
		  <div class="tab-outter">
			<div
				class="tab-cls"
				:class="{ 'tab-active': curTab == 1 }"
				@click="tabChange(1)"
				>
				<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16">
					<g transform="translate(-1782.292 -8)">
					<path
						class="a" d="M1785,24V17h7v7Zm0-16h7v7h-7Zm-9,9h7v7h-7Zm0-9h7v7h-7Z"
						transform="translate(6.292)"
						:style="{
						fill:'#2390FF',
						'fill-rule':'evenodd',
						'margin-right': '6px'
						}"
					/>
					</g>
				</svg>
				<span style="margin-left: 6px; font-size: 16px;">缩略图</span>
			</div
			>
			<div
				class="tab-cls"
				:class="{ 'tab-active': curTab == 2 }"
				@click="tabChange(2)"
				>
				<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16">
				<path
					class="a" d="M1829,23V21h11v2Zm0-8h11v2h-11Zm0-6h11v2h-11Zm-5,11h4v4h-4Zm0-6h4v4h-4Zm0-6h4v4h-4Z"
					transform="translate(-1824 -8)"
					:style="{
					fill:'#2390FF',
					'fill-rule':'evenodd',
					'margin-right': '6px'
					}"
				/>
				</svg>
				<span style="margin-left: 6px; font-size: 16px;">列表</span>
			</div>
		  </div>
		  <p class="tip">请拖拽待安排在押人员到右侧对应床位</p>
		  <div class="tab-cont" style="height: 100%; overflow: auto;">
			<div v-show="curTab == 1">
				<div class="person-list" v-if="roomData.prisoner && roomData.prisoner.notPlan.length > 0">
					<div 
						v-for="person in roomData.prisoner.notPlan" 
						:key="person.id"
						class="person-item"
						draggable="true"
						@dragstart="onDragStart($event, person)"
						>
						<div class="notPlanInfo">
							<img :src="person.frontPhoto" alt="">
							<p>{{ person.jgryxm }}</p>
							<!-- <span class="tabName" v-if="person.riskLevel == '1'" style="background-color: #FA4242;">一</span>
							<span class="tabName" v-if="person.riskLevel == '2'" style="background-color:#FF7700;">二</span>
							<span class="tabName" v-if="person.riskLevel == '3'" style="background-color: #FFBD13;">三</span>
							<span class="tabNames"  v-if="person.isSick && person.isSick != null" :style="{ top: hasTabName ? '27px' : '5px' }">病</span> -->
						</div>
					</div>
				</div>
				<div class="person-lists" v-else>
					<img src="data:image/png;base64,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"
						alt=""
						style="width: 120px; height: 110px;"
					 />
					<p class="bsp-empty-txt">暂无数据</p>
				</div>
			</div>
			<div v-show="curTab == 2">
				<div class="personList">
					<div 
						v-for="person in roomData.prisoner.notPlan" 
						:key="person.id"
						class="person-items"
						draggable="true"
						>
						<!-- <div class="notPlanInfo"> -->
							<!-- {{person}} -->
							<!-- <img :src="person.frontPhoto" alt=""> -->
							<div style="display: flex; align-items: center; position: relative;">
								<p>{{ person.jgryxm ? person.jgryxm : '未知' }}</p>
								<!-- <span v-if="person.riskLevel">{{ getRiskName(person.riskLevel) }}</span>
								<span v-if="person.isSick">病</span> -->
								<span class="tabName" v-if="person.riskLevel == '1'" style="background-color: #FA4242;">一</span>
								<span class="tabName" v-if="person.riskLevel == '2'" style="background-color:#FF7700;">二</span>
								<span class="tabName" v-if="person.riskLevel == '3'" style="background-color: #FFBD13;">三</span>
								<!-- <span class="tabNames"  v-if="person.isSick && person.isSick != null" :style="{ left: hasTabName ? '27px' : '5px' }">病</span> -->
							</div>
							<p style="">入仓时间{{ person.enterDay ? person.enterDay : '0' }}天</p>
						<!-- </div> -->
					</div>
				</div>
			</div>
		  </div>
		</div>
  
		<!-- 右侧：床位布局 -->
		<div class="bed-layout">
		  <div class="action-btn-cont">
			<div @click="bedLayoutCahnge(roomData.rowData.org_code)" style="margin: 0px 30px;" class="actionBtn"><p>床位布局配置</p><img src="@/assets/images/roomManage/shezhitianchong.svg" alt=""></div>
			<div style="margin-left: 8px;" @click="automaticbedChange(roomData.rowData)" class="actionBtn"><p>自动床位配置</p><img  @click.stop="automaticbedEvent" src="@/assets/images/roomManage/shezhitianchong.svg" alt=""></div>
		  </div>
		  <div class="tooltip-container" v-if="isPermission">
			<div 
				class="room-background"
				@dragover.prevent
				@drop="onDrop($event)"
			>
				<img v-if="roomBgImgUrl" :src="roomBgImgUrl" alt="">
				<!-- 床位区域 -->
				<div 
					v-for="(bed, index) in roomData.getByRoomId.layoutConfigs" 
					:key="bed.id"
					class="bed-area"
					@mouseenter="handleMouseEnter(bed.id)" 
					@mouseleave="handleMouseLeave"
					@dragover.prevent
					:style="getBedStyle(bed)"
					@drop="onBedDrop($event, bed)"
					>
					<div style="overflow: hidden;">
						<!-- 固定床位的宽度 -->
						<div :style="getBedStyle1(bed)" v-if="bed.bedType == '0'">
							<div 
								v-if="bed.bedList"
								v-for="(i, n) in bed.bedList"
								:key="n"
								draggable="true"
								class="bedItem"
								:style="getBedStyleBg(bed)"
								@dragstart="onOccupiedDragStart($event, i, bed, n)"
								@dragover.prevent
								@drop="onBedDrop($event, i, bed, n)"
							>
							<Tooltip :content="getTooltipContent(i)" placement="bottom" :disabled="!i.enterDay && !i.riskLevelName">
								<div class="bed-bg" :style="{ backgroundImage: `url(${bedBgImgUrl})` }">
									<div class="bed-ryInfo" @mouseenter="showTooltip = true" @mouseleave="showTooltip = false" ref="tabContainer">
										<span class="tabName" v-if="i.rickLevel == '1'" style="background-color: #FA4242;">一</span>
										<span class="tabName" v-if="i.rickLevel == '2'" style="background-color:#FF7700;">二</span>
										<span class="tabName" v-if="i.rickLevel == '3'" style="background-color: #FFBD13;">三</span>
										<span class="tabNames" v-if="i.isSick && i.isSick != null" :style="{ top: hasTabName ? '27px' : '5px' }">病</span>
										<img class="close" v-if="i.jgrybm !== '' && i.jgrybm !== null" @click="closeRyInfo(i,bed)" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAZNJREFUSEu9VjEvQ1EU/s5tpRNS7UtELR0kjYGJyWQTCbFbDCxI/IT+BAkTg8UuJNhMJiYGaWLooiJ9fa2ni0b7jtzWbV7r9XrC7Rvf/e73nfPdc8+5BM3nOM54g6LLBCwy8xSIkk04c4mI7hk4j3D9NJFIPPWioaAF27bHEIllAV5j5qguCCKqA3SERi1rWdZzN/abgF2pLLFHxwAGdcQBa1USvGrF42f+tQ6BUsXd9jzeBSB+Sa7gnhC0k4wP76kfbYGvyE/+QN4WIcErKpOmgPScxUDOb0vhxUZq1AqVSAC2St5HRp5JS6D8dsDsrSs2ueHq+gaZiTRmpie1Ird3D8g95jE/N9sREJE4tEaGNkiWokfRfHe1qI06ER1GVpfgepqKZXcTzPtBYeoIwgQAoi2yy+4FMy/08iGIKBQ5ACK6pKLzKm9hSme0n1DipOdhzgdAQVr0DubYT+WiRCQuJLlModYHAdMWmT9k02Vq/KIZbxV9aXZNkdagMdOu1SUzOnCUiNGR2RYxOfT9Pek/ni2fbwJmLvnex0wAAAAASUVORK5CYII=" alt="">
										<img v-if="i.jgrybm !== null" :src="i.frontPhoto" alt="" style="width: 84px; height: 88px; background: #D8D8D8; border-radius: 4px;">
										<img v-else style="width: 84px; height: 88px; background: #D8D8D8; border-radius: 4px;" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABwCAYAAACAVlfhAAAAAXNSR0IArs4c6QAABDBJREFUeF7t3F9uElEYBfDvwkChgpXWGFJr/F+jiemDPnQBXYG7cQWuRlfgAnzQB2OisWrUWJvG2CJ/Zwozc82dpEqt2ME5X4H08NjMHO78OBfuzBSMiEgQ2NVYwkdWZEOsPev+xkdKAWNaRuRpTryHpZLZNA4zsuEzEVtLGcHN/ipgGnnjrZteMHhsrX1ApewCxpgnphsMmpzm2TGTBDf9u37fguIY40wJiu0BQbGebCjYk6AERQuA8/geSlCwADiODSUoWAAcx4YSFCwAjmNDCQoWAMexoQQFC4Dj2FCCggXAcWwoQcEC4Dg2lKBgAXAcG0pQsAA4jg0lKFgAHMeGEhQsAI5jQwkKFgDHsaEEBQuA406kof0wlkanL1E8mX9FzeeM1CpFKXo5MN/ROHXQbhDK5nZ7YpgHh+xQV5ercqbkqaKqg77+0pTefqR6EGnD5+fycufSQtrN/2s7ddDn7/f+a2BaO92/sagVneQSFMxLUIJmE+CUz+Z3ZG+CEvSwQNpP+UI+lyy+vbwZizCMbHLSMIjiVPudioaemfPk5nJ1bMwDQYf6brst3f3wWNRTAXp75WzmMxh3RvZmq0VQJ3Dvek2MGW+q/ylnrZUXHxoEdQKoaZjm/Rr1XKNeualY2KMO8tSBuosT5WJe3Cd6Lvd7ii8vlo+dqmk22N7zf20Wxzb55Pf70aGLM6gXb2INffmpIfVzZVmsFhPISTwc7F67Lzs/fFm7ovtLIOpT3jVluI2TAD14zpMYizroMKC7ct/xB9IPrcRW9+p9zhgpekYq5cKJXKk/OM4TAW37A/m660snOH7hrdHgSsmTi0tlqZYLGvGHMlVB3drwy/eefGvuqx9Imie4sDAnl87PZ17z/uu5VEE/7HSS8+xpeixWinKtXlEbkhroTiOQrd2e2sCzBK8szUu9VsoSMXJfFdAwiuXV5+bE73SOOmp3B/Tu5QXxFJZxKqA7DV+2dn8vslWqkDF0Zaks9RrmhGJ4KCqgb7+2pO1P5hM9rXO17Mmti/jfTVQBffmxIYNId52ZFm7UdoW8kbWr+LMmFdA0FymygiD21zivJyjilRnKIChBcQKc8jjLJImgBAULgOPYUIKCBcBxM9lQ1A04lOXwjbyZBNUYdBbc4bM4jbGpL+w1Bn3qQIen1TRPeY2xqTQ0S4NmfV+Cgl9BghIULACOY0MJChYAx7GhBAULgOPYUIKCBcBxbChBwQLgODaUoGABcBwbSlCwADiODSUoWAAcx4YSFCwAjmNDCQoWAMexoQQFC4Dj2FCCggXAcWwoQcEC4Dg2lKBgAXAcG0pQsAA4znSDQVOsxX/PGTzQmYgzpmV6weCxtfbBTAx4ygdpjHligsCuRjZ8JmLxXx6fcgDs8Ewjb7z15AfoHGos4SMrssHpPyazm+YiT3PiPSyVzOZPEanYnwm7hbcAAAAASUVORK5CYII=" alt="">
										<p>{{ i.jgryxm  }}</p>
										<span>{{ i.cwh }}</span>
									</div>
								</div>
							</Tooltip>
							</div>
						</div>
						<!-- 竖向排列 -->
						<div :style="getBedStyle1(bed)" v-else>
							<div 
								v-if="bed.bedList"
								v-for="(i, n) in bed.bedList"
								:key="n"
								draggable="true"
								class="bedItem"
								:style="getBedStyleBgs(bed)"
								@dragstart="onOccupiedDragStart($event, i, bed, n)"
								@dragover.prevent
								@drop="onBedDrop($event, i, bed, n)"
							>
							<Tooltip :content="getTooltipContent(i)" placement="bottom" :disabled="!i.enterDay && !i.riskLevelName">
								<div class="bed-bgs" :style="{ backgroundImage: `url(${bedBgImgUrl})` }">
									<div class="bed-ryInfos">
										<span class="tabName" v-if="i.rickLevel == '1'" style="background-color: #FA4242;">一</span>
										<span class="tabName" v-if="i.rickLevel == '2'" style="background-color:#FF7700;">二</span>
										<span class="tabName" v-if="i.rickLevel == '3'" style="background-color: #FFBD13;">三</span>
										<span class="tabNames" v-if="i.isSick && i.isSick != null" :style="{ top: hasTabName ? '27px' : '5px' }">病</span>
										<img class="close" v-if="i.jgrybm !== '' && i.jgrybm !== null" @click="closeRyInfo(i,bed)" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAZNJREFUSEu9VjEvQ1EU/s5tpRNS7UtELR0kjYGJyWQTCbFbDCxI/IT+BAkTg8UuJNhMJiYGaWLooiJ9fa2ni0b7jtzWbV7r9XrC7Rvf/e73nfPdc8+5BM3nOM54g6LLBCwy8xSIkk04c4mI7hk4j3D9NJFIPPWioaAF27bHEIllAV5j5qguCCKqA3SERi1rWdZzN/abgF2pLLFHxwAGdcQBa1USvGrF42f+tQ6BUsXd9jzeBSB+Sa7gnhC0k4wP76kfbYGvyE/+QN4WIcErKpOmgPScxUDOb0vhxUZq1AqVSAC2St5HRp5JS6D8dsDsrSs2ueHq+gaZiTRmpie1Ird3D8g95jE/N9sREJE4tEaGNkiWokfRfHe1qI06ER1GVpfgepqKZXcTzPtBYeoIwgQAoi2yy+4FMy/08iGIKBQ5ACK6pKLzKm9hSme0n1DipOdhzgdAQVr0DubYT+WiRCQuJLlModYHAdMWmT9k02Vq/KIZbxV9aXZNkdagMdOu1SUzOnCUiNGR2RYxOfT9Pek/ni2fbwJmLvnex0wAAAAASUVORK5CYII=" alt="">
										<img v-if="i.jgrybm !== null" :src="i.frontPhoto" alt="" style="width: 88px; height: 84px; background: #D8D8D8; border-radius: 4px;">
										<img v-else style="width: 88px; height: 84px; background: #D8D8D8; border-radius: 4px;" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABwCAYAAACAVlfhAAAAAXNSR0IArs4c6QAABDBJREFUeF7t3F9uElEYBfDvwkChgpXWGFJr/F+jiemDPnQBXYG7cQWuRlfgAnzQB2OisWrUWJvG2CJ/Zwozc82dpEqt2ME5X4H08NjMHO78OBfuzBSMiEgQ2NVYwkdWZEOsPev+xkdKAWNaRuRpTryHpZLZNA4zsuEzEVtLGcHN/ipgGnnjrZteMHhsrX1ApewCxpgnphsMmpzm2TGTBDf9u37fguIY40wJiu0BQbGebCjYk6AERQuA8/geSlCwADiODSUoWAAcx4YSFCwAjmNDCQoWAMexoQQFC4Dj2FCCggXAcWwoQcEC4Dg2lKBgAXAcG0pQsAA4jg0lKFgAHMeGEhQsAI5jQwkKFgDHsaEEBQuA406kof0wlkanL1E8mX9FzeeM1CpFKXo5MN/ROHXQbhDK5nZ7YpgHh+xQV5ercqbkqaKqg77+0pTefqR6EGnD5+fycufSQtrN/2s7ddDn7/f+a2BaO92/sagVneQSFMxLUIJmE+CUz+Z3ZG+CEvSwQNpP+UI+lyy+vbwZizCMbHLSMIjiVPudioaemfPk5nJ1bMwDQYf6brst3f3wWNRTAXp75WzmMxh3RvZmq0VQJ3Dvek2MGW+q/ylnrZUXHxoEdQKoaZjm/Rr1XKNeualY2KMO8tSBuosT5WJe3Cd6Lvd7ii8vlo+dqmk22N7zf20Wxzb55Pf70aGLM6gXb2INffmpIfVzZVmsFhPISTwc7F67Lzs/fFm7ovtLIOpT3jVluI2TAD14zpMYizroMKC7ct/xB9IPrcRW9+p9zhgpekYq5cKJXKk/OM4TAW37A/m660snOH7hrdHgSsmTi0tlqZYLGvGHMlVB3drwy/eefGvuqx9Imie4sDAnl87PZ17z/uu5VEE/7HSS8+xpeixWinKtXlEbkhroTiOQrd2e2sCzBK8szUu9VsoSMXJfFdAwiuXV5+bE73SOOmp3B/Tu5QXxFJZxKqA7DV+2dn8vslWqkDF0Zaks9RrmhGJ4KCqgb7+2pO1P5hM9rXO17Mmti/jfTVQBffmxIYNId52ZFm7UdoW8kbWr+LMmFdA0FymygiD21zivJyjilRnKIChBcQKc8jjLJImgBAULgOPYUIKCBcBxM9lQ1A04lOXwjbyZBNUYdBbc4bM4jbGpL+w1Bn3qQIen1TRPeY2xqTQ0S4NmfV+Cgl9BghIULACOY0MJChYAx7GhBAULgOPYUIKCBcBxbChBwQLgODaUoGABcBwbSlCwADiODSUoWAAcx4YSFCwAjmNDCQoWAMexoQQFC4Dj2FCCggXAcWwoQcEC4Dg2lKBgAXAcG0pQsAA4znSDQVOsxX/PGTzQmYgzpmV6weCxtfbBTAx4ygdpjHligsCuRjZ8JmLxXx6fcgDs8Ewjb7z15AfoHGos4SMrssHpPyazm+YiT3PiPSyVzOZPEanYnwm7hbcAAAAASUVORK5CYII=" alt="">
										<!-- <img :src="i.frontPhoto" alt="" style="width: 88px; height: 84px; background: #D8D8D8; border-radius: 4px;"> -->
										<div style="display: flex; flex-direction: column; justify-content: space-around; align-items: center; margin-left: 4px;">
											<p>{{ i.jgryxm  }}</p>
											<span>{{ i.cwh }}</span>
										</div>
									</div>
								</div>
							</Tooltip>
							</div>
						</div>
					</div>

					<!-- 可添加床位区域 -->
					<div 
						v-if="bed.isAllowedAdd"
						class="add-bed-area"
						:style="getAddBedStyle(bed)"
					>
						<div class="add-bedCont" @click="addNewBed(bed)">
							<Icon type="ios-add" size="35" color="#fff" />
							<div class="add-bed-btn">添加空床位</div>
						</div>
						<Button type="primary" @click="getJgryInfo(bed)" style="width: 100px; height: 32px;">查看全部</Button>
					</div>
					<!-- 滚动按钮 -->
					<div v-show="bed.bedType == '0'">
						<span v-if="showScrollArrows(bed, 'left')" class="scroll-bar scroll-bar-left" @click="scrollBed(bed, 'left')"> 
							<img src="@/assets/images/roomManage/left_arrow_icon.png" > 
						</span>
						<span v-if="showScrollArrows(bed, 'right')" class="scroll-bar scroll-bar-right"  @click="scrollBed(bed, 'right')"> 
							<img src="@/assets/images/roomManage/right_arrow_icon.png" alt=""> 
						</span>
						</div>
						<div v-show="bed.bedType == '1'">
						<span v-if="showScrollArrows(bed, 'top')" class="scroll-bar scroll-bar-left" @click="scrollBed(bed, 'top')"> 
							<Icon type="md-arrow-dropup" size="20" color="#fff" />
						</span>
						<span v-if="showScrollArrows(bed, 'bottom')" class="scroll-bar scroll-bar-right"  @click="scrollBed(bed, 'bottom')"> 
							<Icon type="md-arrow-dropdown" size="20" color="#fff" />
						</span>
					</div>

					
				</div>
			</div>
		  </div>
		  <div class="tooltip-container" v-if="!isPermission">
			<div 
				class="room-backgrounds"
				@dragover.prevent
				@drop="onDrop($event)"
			>
				<div class="roomBed-content">
					<!-- {{ customData.layoutColumn }}
					{{ customData.layoutRow }} -->
					<div class="roomBed-grid" :style="gridStyle(formData)">
						<div 
							v-if="customData.bedList"
							v-for="(item,index) in customData.bedList" :key="index"
							draggable="true"
							class="bedItem"
							:style="gridStyles(item,customData)"
							@dragstart="onOccupiedDragStarts($event, item, customData, index)"
							@dragover.prevent
							@drop="onBedDrops($event, item, customData, index)"
						>
							<Tooltip :content="getTooltipContent(item)" placement="bottom" :disabled="!item.enterDay && !item.riskLevelName">
								<div class="bed-bg" :style="{ backgroundImage: `url(${bedBgImgUrl})` }">
									<div class="bed-ryInfo" @mouseenter="showTooltip = true" @mouseleave="showTooltip = false" ref="tabContainer">
										<img class="close" v-if="item.jgrybm !== '' && item.jgrybm !== null" @click="closeRyInfos(item,customData)" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAZNJREFUSEu9VjEvQ1EU/s5tpRNS7UtELR0kjYGJyWQTCbFbDCxI/IT+BAkTg8UuJNhMJiYGaWLooiJ9fa2ni0b7jtzWbV7r9XrC7Rvf/e73nfPdc8+5BM3nOM54g6LLBCwy8xSIkk04c4mI7hk4j3D9NJFIPPWioaAF27bHEIllAV5j5qguCCKqA3SERi1rWdZzN/abgF2pLLFHxwAGdcQBa1USvGrF42f+tQ6BUsXd9jzeBSB+Sa7gnhC0k4wP76kfbYGvyE/+QN4WIcErKpOmgPScxUDOb0vhxUZq1AqVSAC2St5HRp5JS6D8dsDsrSs2ueHq+gaZiTRmpie1Ird3D8g95jE/N9sREJE4tEaGNkiWokfRfHe1qI06ER1GVpfgepqKZXcTzPtBYeoIwgQAoi2yy+4FMy/08iGIKBQ5ACK6pKLzKm9hSme0n1DipOdhzgdAQVr0DubYT+WiRCQuJLlModYHAdMWmT9k02Vq/KIZbxV9aXZNkdagMdOu1SUzOnCUiNGR2RYxOfT9Pek/ni2fbwJmLvnex0wAAAAASUVORK5CYII=" alt="">
										<img v-if="item.jgrybm !== null" :src="item.frontPhoto" alt="" style="width: 84px; height: 88px; background: #D8D8D8; border-radius: 4px;">
										<img v-else style="width: 84px; height: 88px; background: #D8D8D8; border-radius: 4px;" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABwCAYAAACAVlfhAAAAAXNSR0IArs4c6QAABDBJREFUeF7t3F9uElEYBfDvwkChgpXWGFJr/F+jiemDPnQBXYG7cQWuRlfgAnzQB2OisWrUWJvG2CJ/Zwozc82dpEqt2ME5X4H08NjMHO78OBfuzBSMiEgQ2NVYwkdWZEOsPev+xkdKAWNaRuRpTryHpZLZNA4zsuEzEVtLGcHN/ipgGnnjrZteMHhsrX1ApewCxpgnphsMmpzm2TGTBDf9u37fguIY40wJiu0BQbGebCjYk6AERQuA8/geSlCwADiODSUoWAAcx4YSFCwAjmNDCQoWAMexoQQFC4Dj2FCCggXAcWwoQcEC4Dg2lKBgAXAcG0pQsAA4jg0lKFgAHMeGEhQsAI5jQwkKFgDHsaEEBQuA406kof0wlkanL1E8mX9FzeeM1CpFKXo5MN/ROHXQbhDK5nZ7YpgHh+xQV5ercqbkqaKqg77+0pTefqR6EGnD5+fycufSQtrN/2s7ddDn7/f+a2BaO92/sagVneQSFMxLUIJmE+CUz+Z3ZG+CEvSwQNpP+UI+lyy+vbwZizCMbHLSMIjiVPudioaemfPk5nJ1bMwDQYf6brst3f3wWNRTAXp75WzmMxh3RvZmq0VQJ3Dvek2MGW+q/ylnrZUXHxoEdQKoaZjm/Rr1XKNeualY2KMO8tSBuosT5WJe3Cd6Lvd7ii8vlo+dqmk22N7zf20Wxzb55Pf70aGLM6gXb2INffmpIfVzZVmsFhPISTwc7F67Lzs/fFm7ovtLIOpT3jVluI2TAD14zpMYizroMKC7ct/xB9IPrcRW9+p9zhgpekYq5cKJXKk/OM4TAW37A/m660snOH7hrdHgSsmTi0tlqZYLGvGHMlVB3drwy/eefGvuqx9Imie4sDAnl87PZ17z/uu5VEE/7HSS8+xpeixWinKtXlEbkhroTiOQrd2e2sCzBK8szUu9VsoSMXJfFdAwiuXV5+bE73SOOmp3B/Tu5QXxFJZxKqA7DV+2dn8vslWqkDF0Zaks9RrmhGJ4KCqgb7+2pO1P5hM9rXO17Mmti/jfTVQBffmxIYNId52ZFm7UdoW8kbWr+LMmFdA0FymygiD21zivJyjilRnKIChBcQKc8jjLJImgBAULgOPYUIKCBcBxM9lQ1A04lOXwjbyZBNUYdBbc4bM4jbGpL+w1Bn3qQIen1TRPeY2xqTQ0S4NmfV+Cgl9BghIULACOY0MJChYAx7GhBAULgOPYUIKCBcBxbChBwQLgODaUoGABcBwbSlCwADiODSUoWAAcx4YSFCwAjmNDCQoWAMexoQQFC4Dj2FCCggXAcWwoQcEC4Dg2lKBgAXAcG0pQsAA4znSDQVOsxX/PGTzQmYgzpmV6weCxtfbBTAx4ygdpjHligsCuRjZ8JmLxXx6fcgDs8Ewjb7z15AfoHGos4SMrssHpPyazm+YiT3PiPSyVzOZPEanYnwm7hbcAAAAASUVORK5CYII=" alt="">
										<p>{{ item.jgryxm  }}</p>
										<span>{{ item.cwh }}</span>
									</div>
								</div>
							</Tooltip>
						</div>
					</div>
					<!-- <Grid :col="customData.layoutColumn" v-for="(item,index) in customData.bedList" :key="index">
						<GridItem>{{item.cwh}}</GridItem>
					</Grid> -->
					<!-- <Grid :col="4">
						<GridItem>1</GridItem>
						<GridItem>2</GridItem>
						<GridItem>3</GridItem>
						<GridItem>4</GridItem>
						<GridItem>5</GridItem>
						<GridItem>6</GridItem>
						<GridItem>7</GridItem>
						<GridItem>8</GridItem>
					</Grid> -->
				</div>
				<!-- <img v-if="BgImgUrl" style="width: 100%; height: 100%;" :src="BgImgUrl" alt=""> -->
				<!-- 床位区域 -->
				
			</div>
		  </div>
		</div>
	  </div>

	    <!-- 自动床位配置 -->
	    <Modal
			v-model="automaticBedModal"
			:mask-closable="false"
			:closable="false"
			width="860"
		>
			<div class="flow-modal-title" slot="header">
				<span style="font-size: 17px;">自动床位规则</span>
				<span @click="automaticBedCancel" style="position: absolute; right: 6px;cursor: pointer;">
					<i class="ivu-icon ivu-icon-ios-close"></i>
				</span>
			</div>
			<div class="select-use radio-group-container" style="color: #2b2b2b;">
				<RadioGroup v-model="vertical" vertical v-for="(e,i) in cwglList" :key="i">
					<div style="margin-bottom: 10px;">
						<Radio :label="e.code">
							<span>{{ e.name }}</span>
						</Radio>
					</div>
				</RadioGroup>
				<!-- <CheckboxGroup v-model="selectedOptions" v-for="(e, i) in cwglList" :key="i">
					<div style="margin-bottom: 10px;">
						<Checkbox :label="e.code">
							<span>{{ e.name }}</span>
						</Checkbox>
					</div>
				</CheckboxGroup> -->
				<div style="margin-top: 20px;">
					<p style="line-height: 35px; position: absolute; bottom: 0; color: #8D99A5;">注：提交后将重新对当前监室自动安排床位</p>
				</div>
			</div>
			<div slot="footer">
				<Button type="primary" @click="automaticBedOk(roomData.rowData)" class="sure_btn">确 定</Button>
				<Button @click="automaticBedCancel" class="cancle_btn">取 消</Button>
			</div>
	    </Modal>

		<!-- 床位布局配置管理 -->
	    <Modal
			v-model="bedInformationModal"
			:mask-closable="false"
			:closable="false"
			class-name="select-use-modals"
			width="1100"
		>
			<div class="flow-modal-title" slot="header">
				<span style="font-size: 17px;">床位信息配置</span>
				<span @click="bedInformatioCancel" style="position: absolute; right: 6px;cursor: pointer;">
					<i class="ivu-icon ivu-icon-ios-close"></i>
				</span>
			</div>
			<div class="bed-config" style="color: #2b2b2b;">
				<Form ref="formData" :model="formData" :label-width="120" :label-colon="true" >
					<Row>
						<Col span="4" style="font-size: 16px; color: #00244A; font-family: MicrosoftYaHei, MicrosoftYaHei; text-align: center;">床位布局类型</Col>
						<Col span="20">
							<div style="width: 100%;height: 450px; overflow-y: auto; display: flex; flex-direction: row; flex-wrap: wrap; ">
								<div v-for="(item,index) in orgBedList" :key="index" style="width: 17%;">
									<div class="orgBed" :class="{ 'active-border': activeItem === item.id }" @click="getBedInfo(item)" v-if="item.layoutType == '02'">
										<img src="../../assets/images/roomManage/zidingyi_1.png" alt="">
										<span>自定义床位布局</span>
									</div>
									<div v-else class="orgBed-class" :class="{ 'active-border': activeItem === item.id }" @click="getBedInfo(item)">
										<img :src="item.layoutImageUrl" alt="">
										<span>{{ item.layoutName }}</span>
									</div>
								</div>
							</div>
						</Col>
					</Row>
					<Row style="margin-top: 20px;" v-if="isCustom">
						<Col span="4" style="font-size: 16px; color: #00244A; font-family: MicrosoftYaHei, MicrosoftYaHei; text-align: center;">
							<!-- <Button type="primary" @click="customizeClick">自定义床位布局</Button> -->
							自定义床位布局
						</Col>
						<Col span="20">
							<div style="display: flex; flex-direction: column;">
								<!-- <span style="font-size: 12px; color: #f00;">请输入您想要自定义的床位布局是几行几列（提示：最多不能超过5行5列）</span> -->
								<p style="font-size: 16px; color: rgb(0, 36, 74); font-family: MicrosoftYaHei, MicrosoftYaHei;">
									<InputNumber v-model="formData.layoutRow" class="bedSize" min="1" max="10" />&nbsp;&nbsp;行&nbsp;&nbsp;
									<InputNumber v-model="formData.layoutColumn" class="bedSize" min="1" max="10" />&nbsp;&nbsp;列
								</p>
							</div>
						</Col>
					</Row>

					<!-- <Row style="margin-top: 20px">
						<Col span="4" style="font-size: 16px; color: #00244A; font-family: MicrosoftYaHei, MicrosoftYaHei; text-align: center;">床位规格</Col>
						<Col span="20" style="display: flex; flex-direction: column;">
							<div style="display: flex; flex-direction: column;">
								<span style="font-size: 12px; color: #a09e9e;">长x宽x高</span>
								<p>
									<input v-model="formData.bedSpaceLength" type="text" class="bedSize">*<input v-model="formData.bedSpaceWidth" type="text" class="bedSize">*<input type="text" v-model="formData.bedSpaceHeight" class="bedSize" style="border-radius: 4px 0px 0px 4px;"><span class="dw">cm</span>
								</p>
							</div>
							<div style="display: flex; flex-direction: column;">
								<span style="font-size: 12px; color: #a09e9e;">称重</span>
								<p><input type="text" v-model="formData.bedSpaceBearing" class="zl" style="width: 180px; height: 36px; border-color: #1890ff; border-radius: 4px 0px 0px 4px;"><span class="dw">kg</span></p>
							</div>
						</Col>
					</Row> -->
				</Form>
			</div>
			<div slot="footer">
				<Button @click="bedInformatioCancel" class="cancle_btn">取 消</Button>
				<Button type="primary" @click="bedInformatioOk" class="sure_btn">确 定</Button>
			</div>
	    </Modal>

		<!-- 查看全部人员 -->
	    <Modal
			v-model="bedRyInfonModal"
			:mask-closable="false"
			:closable="false"
			width="660"
		>
			<div class="flow-modal-title" slot="header">
				<span style="font-size: 17px;">床位信息配置</span>
				<span @click="bedRyInfonCancel" style="position: absolute; right: 6px;cursor: pointer;">
					<i class="ivu-icon ivu-icon-ios-close"></i>
				</span>
			</div>
			<div class="select-use" style="color: #2b2b2b;">
				1
			</div>
			<div slot="footer">
				<Button @click="bedRyInfonCancel" class="cancle_btn">取 消</Button>
				<!-- <Button type="primary" @click="bedRyInfonOk" class="save">确 定</Button> -->
			</div>
	    </Modal>
	</div>
  </template>
  
  <script>
   import { mapActions } from 'vuex'
  export default {
	components: {
	},
	props: {
	  roomDatas: Object
	},
	data() {
	  return {
		roomBgImgUrl: '',
		unassignedPersons: [],
		bedLayouts: [],
		rowData: {},
		currentDragPerson: null,
		currentDragBed: null,
		modalTitle: '',
		curTab: 1,
		bedBgImgUrl: require('@/assets/images/roomManage/单个床.png'),
		BgImgUrl: require('@/assets/images/roomManage/围墙.png'),
		automaticBedModal: false,
		vertical: '01',
		bedInformationModal: false,
		bedRyInfonModal: false,
		isHover: false,
		hoverBedId: null,
		showTooltip: null,
		cwglList: [],
		orgBedList: [],
		formData: {},
		activeItem: null, 
		roomData: {},
		scrollStates: {}, // 存储每个床位的滚动状态
		dragState: {
			sourceBed: null,
			sourceIndex: null,
			sourceItem: null
		},
		isCustom: false,
		isPermission: false,
		customData:{},
		orgCode: this.$store.state.common.orgCode,
	  }
	},
	watch:{
		roomDatas: {
			handler(val){
				if(val) {
					this.roomData = this.roomDatas
					this.initData()
				}
			},
			deep: true,
			immediate: true // 立即执行
		}
	},
	created() {
		// console.log(this.org.includes(this.org),'vvvvvvvvvvv');
		// if(this.orgCode && this.org.includes(this.orgCode)) {
		// 	console.log('已有');
		// 	this.isPermission = true
		// } else {
		// 	console.log('自定义');
		// 	this.isPermission = false
		// }
		// console.log(this.roomData ,'roomData111');
	//   this.initData();
	},
	computed: {
		hasTabName() {
			// 通过 $refs.tabContainer 获取父元素，判断是否有 .tabName 子元素
			// return this.$refs.tabContainer && this.$refs.tabContainer.querySelector('.tabName') !== null;
		}
	},

	methods: {
	  ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
	  initData() {
		console.log(this.roomData,'iniftData');
		if(this.roomData.rowData){
			this.modalTitle = this.roomData.rowData.room_name
			this.rowData = this.roomData.rowData
		}

		// console.log(this.roomData.getByRoomId,'this.roomData.getByRoomId');
		if (this.roomData.getByRoomId) {
			if(this.roomData.getByRoomId.layoutType == '02'){
				this.isPermission = false
				console.log('自定义');
				this.customData = this.roomData.getByRoomId.layoutConfigs[0]
				this.isCustom = true
				this.formData.layoutRow = this.roomData.getByRoomId.layoutRow
				this.formData.layoutColumn = this.roomData.getByRoomId.layoutColumn
			} else if(this.roomData.getByRoomId.layoutType == '01') {
				this.formData.layoutId = this.roomData.getByRoomId.layoutId;
				this.isPermission = true
				console.log('已有');
			}
			this.activeItem = this.roomData.getByRoomId.layoutId;
			this.vertical = this.roomData.getByRoomId.bedAutoConfig
			console.log(this.roomData.getByRoomId,'this.roomData.getByRoomId');
		  this.roomBgImgUrl = this.roomData.getByRoomId ? this.roomData.getByRoomId.layoutUrl +'?' + new Date().getTime() : '';
		  console.log(this.roomBgImgUrl,'this.roomBgImgUrl1111111111111111');
		  this.bedLayouts = this.roomData.getByRoomId.layoutConfigs || [];
		}
		
		if (this.roomData.prisoner && this.roomData.prisoner.notPlan) {
			this.unassignedPersons = this.roomData.prisoner.notPlan
			console.log(this.unassignedPersons);
		}
		if (this.roomData.getByRoomId && this.roomData.getByRoomId.layoutConfigs) {
			this.roomData.getByRoomId.layoutConfigs.forEach(bed => {
				this.$set(this.scrollStates, bed.id, {
					offset: 0,
					maxOffset: 0
				});
			});
		}
	  },
	  // 计算滚动状态
	  calculateScrollState(bed) {
		if (!this.scrollStates[bed.id]) {
			this.$set(this.scrollStates, bed.id, {
			offset: 0,
			maxOffset: 0
			});
		}
		
		const bedWidth = bed.bedType === '0' ? 116 : 184;
		const visibleBeds = bed.entityBedCount;
		const totalBeds = bed.bedList.length;
		
		// 计算最大偏移量
		this.scrollStates[bed.id].maxOffset = Math.max(0, (totalBeds - visibleBeds) * bedWidth);
	  },
	  // 判断是否显示滚动箭头
	  showScrollArrows(bed, direction) {
		// console.log(bed, direction,'bed, direction');
		if (!this.scrollStates[bed.id]) return false;
		if (!this.hoverBedId || this.hoverBedId !== bed.id) return false;
		
		const { offset, maxOffset } = this.scrollStates[bed.id];
		
		switch (direction) {
			case 'left':
			case 'top':
			return offset > 0;
			case 'right':
			case 'bottom':
			return offset < maxOffset;
			default:
			return false;
		}
    },
	// 滚动床位
    scrollBed(bed, direction) {
      if (!this.scrollStates[bed.id]) return;
      
      const bedWidth = bed.bedType === '0' ? 116 : 116; // 竖向滚动步长为116
      const scrollAmount = bed.bedType === '0' ? 116 : 116;
      
      let newOffset = this.scrollStates[bed.id].offset;
      
      switch (direction) {
        case 'left':
        case 'top':
          newOffset = Math.max(0, newOffset - scrollAmount);
          break;
        case 'right':
        case 'bottom':
          newOffset = Math.min(this.scrollStates[bed.id].maxOffset, newOffset + scrollAmount);
          break;
      }
      
      this.scrollStates[bed.id].offset = newOffset;
    },

	tabChange(val) {
	this.curTab = val;
	},
	  
	// 获取床位样式
	getBedStyle(bed) {
	// console.log(bed,bed.y1,bed.x1,'bed');
	const style = {
		position: 'absolute',
		display: 'flex',
		top: `${bed.y1}px`,
		left: `${bed.x1}px`,
	//   height: '168px',
	//   overflow: 'hidden'
	};
	if(bed.bedType == '0') {
		style.width = 116 * bed.entityBedCount + 'px'
		style.height = '168px'
	} else if (bed.bedType == '1') {
		style.width = '184px'
		style.height = 116 * bed.entityBedCount + 'px'
	}
	// 根据bedType设置方向
	// if (bed.bedType === '1') { // 竖向排列
	// //   style.width = '168px';
	//   style.height = 168 * bed.entityBedCount + 'px',
	//   style.flexDirection= 'column'
	// }
	
	return style;
	},
	getBedStyle1(bed){
	const style = {
		display: 'flex',
		flexDirection: bed.bedType == '0' ? 'row' : 'column',
		position: 'relative',
		transition: 'transform 0.3s ease'
	}
	// 应用滚动偏移
	if (this.scrollStates[bed.id]) {
		if (bed.bedType == '0') {
			style.transform = `translateX(-${this.scrollStates[bed.id].offset}px)`;
		} else {
			style.transform = `translateY(-${this.scrollStates[bed.id].offset}px)`;
		}
	}
	if(bed.bedType == '0') {
		style.width = 116 * bed.entityBedCount + 'px'
		style.height = '168px'
	} else if (bed.bedType == '1') {
		style.width = '184px'
		style.height = 116 * bed.entityBedCount + 'px'
	}
	return style
	},
	getBedStyle2(bed){
	console.log(this.bedBgImgUrl,'this.bedBgImgUrl');
	const style = {
		width: '116px',
		height: '168px',
		// backgroundImage: `url(${this.bedBgImgUrl})`,
		// backgroundImage: `url(${require('@/assets/images/roomManage/单个床 (1).png')})`,
		// backgroundSize: '100% 100%',
		// backgroundRepeat: 'no-repeat'
	}
	return style;
	},
	
	getBedStyleBg(bed){
	const style = {
		width: '116px',
		height: '168px',
		// backgroundImage: `url(${require('@/assets/images/roomManage/单个床.png')})`,
		// backgroundRepeat: 'no-repeat'
	}
	return style
	},
	getBedStyleBgs(bed){
	const style = {
		width: '184px',
		height: '116px',
		// backgroundImage: `url(${require('@/assets/images/roomManage/单个床.png')})`,
		// backgroundRepeat: 'no-repeat'
	}
	return style
	},
	getScrollBarStyle(bed, side) {
	const baseStyle = {
		position: 'absolute',
		width: '32px',
		height: '50px',
		background: 'rgba(0, 0, 0, 0.4)',
		cursor: 'pointer',
		zIndex: 3,
		textAlign: 'center',
		lineHeight: '50px',
		borderRadius: '4px'
	};

	// 处理添加空床位时的偏移
	let positionOffset = 0;
	if (bed.isAllowedAdd) {
		positionOffset = 116; // 空床位宽度/高度
	}

	// 根据床位类型和方向返回不同样式
	if (bed.bedType === '0') {
		if (side === 'left') {
		return {
			...baseStyle,
			left: `${-positionOffset}px`,
			top: '50%',
			transform: 'translateY(-50%)',
			borderRadius: '0 4px 4px 0'
		};
		} else {
		return {
			...baseStyle,
			right: `${-positionOffset}px`,
			top: '50%',
			transform: 'translateY(-50%)',
			borderRadius: '4px 0 0 4px'
		};
		}
	} else {
		if (side === 'top') {
		return {
			...baseStyle,
			top: `${-positionOffset}px`,
			left: '50%',
			transform: 'translateX(-50%)',
			borderRadius: '0 0 4px 4px'
		};
		} else {
		return {
			...baseStyle,
			bottom: `${-positionOffset}px`,
			left: '50%',
			transform: 'translateX(-50%)',
			borderRadius: '4px 4px 0 0'
		};
		}
	}
	},
	getTooltipStyle(bed) {
		// 根据床位位置计算Tooltip应该出现的位置
		return {
		position: 'absolute',
		top: `${bed.y1 + 168}px`, // 调整这个值
		left: `${bed.x1}px`,
		zIndex: 999
		};
	},

	// 获取可添加床位区域样式
	getAddBedStyle(bed) {
	const style = {
		position: 'absolute',
		width: '116px',
		height: '168px',
		backgroundImage: `url(${this.bedBgImgUrl})`
	};

	if(bed.addLocation == 'left') {
		style.left =  '-116px'
	} else if(bed.addLocation == 'right') {
		style.right =  '-116px'
	} else if(bed.addLocation == 'top') {
		style.top =  '-168px'
	} else if(bed.addLocation == 'bottom') {
		style.bottom =  '-168px'
	}
	
	// if (bed.bedType === '0') { // 横向排列
	//   style.left = '116px';
	//   style.top = '0';
	// } else { // 竖向排列
	//   style.top = '116px';
	//   style.left = '0';
	//   style.width = '168px';
	//   style.height = '116px';
	// }
	
	return style;
	},

	
	gridStyle(data){
		return {
			'display': 'grid',
			'grid-template-rows': `repeat(${data.layoutRow}, 168px)`,
			'grid-template-columns': `repeat(${data.layoutColumn}, 116px)`,
			'justify-items': 'center',
			'justify-content': 'center'
		}
	},
	gridStyles(item) {
		// console.log(item,'item');
		return {
			'width': '116px',
			'height': '168px'
		}
	},
	
	// 拖拽开始（待安排人员）
	onDragStart(event, person) {
		console.log(event, person,'event, person');
		this.dragState = {
			type: 'unassigned',
			person: person
		};
		event.dataTransfer.setData('text/plain', person.id);
	},
	onOccupiedDragStart(event, item, bed, index) {
		console.log('item',item);
		console.log('bed',bed);
		console.log('index',index);
		console.log(item,'当前拖拽的元素');
		if (!item.jgrybm) {
			this.$Message.error('空床位不可以拖动')
			return;
		}
      
		this.dragState = {
			type: 'occupied',
			sourceBed: bed,
			sourceIndex: index,
			sourceItem: item
		};
	  console.log(this.dragState,'1111');
      event.dataTransfer.setData('text/plain', item.id);
	},
	onBedDrops(event, targetItem, targetBed, targetIndex) {
	  console.log( targetItem,'放置到那个元素');
	  console.log('拖动过来的元素',this.dragState);
      event.preventDefault();
      
      if (!this.dragState) return;
      
      // 从待安排人员拖拽到床位
      if (this.dragState.type === 'unassigned') {
		console.log('unassigned');
		// 从左侧待安排人员拖动过来放置到空的床位
        if (!targetItem.jgrybm) {
			console.log('从左侧待安排人员拖动过来放置到空的床位','右侧',targetItem);
			const bedFields = { //保留床位信息
				cwh: targetItem.cwh,
				id: targetItem.id,
				isEntity: targetItem.isEntity
			};
          Object.assign(targetItem, this.dragState.person); // 复制人员信息到床位
		  Object.assign(targetItem, bedFields);
          
          // 从待安排列表移除
          this.roomData.prisoner.notPlan = this.roomData.prisoner.notPlan.filter(
            p => p.id !== this.dragState.person.id
          );
        } 
        // 从左侧待安排人员拖动过来放置到有人的床位（交换）
        else {
        //   const originalOccupant = { ...targetItem };
		//   console.log(originalOccupant,'originalOccupant右侧床位原来的人');
		//   console.log(this.dragState.person,'originalOccupant左侧拖动过来的人');
          
        //   Object.assign(targetItem, this.dragState.person);
          
        //   // 从待安排列表移除
        //   this.roomData.prisoner.notPlan = this.roomData.prisoner.notPlan.filter(
        //     p => p.id !== this.dragState.person.id
        //   );
		//   // 将原床位人员移到待安排列表
        //   this.roomData.prisoner.notPlan.push(originalOccupant);
		//   console.log(this.roomData.prisoner.notPlan,'notPlan');

		   const dragPersonIndex = this.roomData.prisoner.notPlan.findIndex(p => p.id === this.dragState.person.id);
			if (dragPersonIndex !== -1) {
				const bedFields = { //保留床位信息
					cwh: targetItem.cwh,
					id: targetItem.id,
					isEntity: targetItem.isEntity
				};
				const originalOccupant = { ...targetItem };
				Object.assign(targetItem, this.dragState.person);
				Object.assign(targetItem, bedFields);
				const occupantForNotPlan = { ...originalOccupant };
				delete occupantForNotPlan.cwh;
				delete occupantForNotPlan.id;
				delete occupantForNotPlan.isEntity;
				this.roomData.prisoner.notPlan.splice(dragPersonIndex, 1, originalOccupant);
				console.log(this.roomData.prisoner.notPlan,'this.roomData.prisoner.notPlan');
			}
        }
      }
      else if (this.dragState.type === 'occupied') {// 从床位拖拽到床位（交换）
		console.log('occupied');
        [this.dragState.sourceItem.jgrybm, targetItem.jgrybm] = [targetItem.jgrybm, this.dragState.sourceItem.jgrybm];
          
        [this.dragState.sourceItem.jgryxm, targetItem.jgryxm] = [targetItem.jgryxm, this.dragState.sourceItem.jgryxm];
          
        [this.dragState.sourceItem.frontPhoto, targetItem.frontPhoto] = [targetItem.frontPhoto, this.dragState.sourceItem.frontPhoto];
        
		[this.dragState.sourceItem.enterDay, targetItem.enterDay] =  [targetItem.enterDay, this.dragState.sourceItem.enterDay];
		[this.dragState.sourceItem.isEntity, targetItem.isEntity] =  [targetItem.isEntity, this.dragState.sourceItem.isEntity];
		[this.dragState.sourceItem.isSick, targetItem.isSick] =  [targetItem.isSick, this.dragState.sourceItem.isSick];
		[this.dragState.sourceItem.riskLevel, targetItem.riskLevel] =  [targetItem.riskLevel, this.dragState.sourceItem.riskLevel];
		[this.dragState.sourceItem.riskLevelName, targetItem.riskLevelName] =  [targetItem.riskLevelName, this.dragState.sourceItem.riskLevelName];
      }
      
      this.dragState = null;
    },
    
    // 拖拽开始（已占用床位）
    onOccupiedDragStarts(event, item, bed, index) {
		console.log('item',item);
		console.log('bed',bed);
		console.log('index',index);
		console.log(item,'当前拖拽的元素');
		if (!item.jgrybm) {
			this.$Message.error('空床位不可以拖动')
			return;
		}
      
		this.dragState = {
			type: 'occupied',
			sourceBed: bed,
			sourceIndex: index,
			sourceItem: item
		};
	  console.log(this.dragState,'1111');
      event.dataTransfer.setData('text/plain', item.id);
    },
	// 放置到床位
    onBedDrop(event, targetItem, targetBed, targetIndex) {
	  console.log( targetItem,'放置到那个元素');
	  console.log('拖动过来的元素',this.dragState);
      event.preventDefault();
      
      if (!this.dragState) return;
      
      // 从待安排人员拖拽到床位
      if (this.dragState.type === 'unassigned') {
		console.log('unassigned');
		// 从左侧待安排人员拖动过来放置到空的床位
        if (!targetItem.jgrybm) {
			console.log('从左侧待安排人员拖动过来放置到空的床位','右侧',targetItem);
			const bedFields = { //保留床位信息
				cwh: targetItem.cwh,
				id: targetItem.id,
				isEntity: targetItem.isEntity
			};
          Object.assign(targetItem, this.dragState.person); // 复制人员信息到床位
		  Object.assign(targetItem, bedFields);
          
          // 从待安排列表移除
          this.roomData.prisoner.notPlan = this.roomData.prisoner.notPlan.filter(
            p => p.id !== this.dragState.person.id
          );
        } 
        // 从左侧待安排人员拖动过来放置到有人的床位（交换）
        else {
        //   const originalOccupant = { ...targetItem };
		//   console.log(originalOccupant,'originalOccupant右侧床位原来的人');
		//   console.log(this.dragState.person,'originalOccupant左侧拖动过来的人');
          
        //   Object.assign(targetItem, this.dragState.person);
          
        //   // 从待安排列表移除
        //   this.roomData.prisoner.notPlan = this.roomData.prisoner.notPlan.filter(
        //     p => p.id !== this.dragState.person.id
        //   );
		//   // 将原床位人员移到待安排列表
        //   this.roomData.prisoner.notPlan.push(originalOccupant);
		//   console.log(this.roomData.prisoner.notPlan,'notPlan');

		   const dragPersonIndex = this.roomData.prisoner.notPlan.findIndex(p => p.id === this.dragState.person.id);
			if (dragPersonIndex !== -1) {
				const bedFields = { //保留床位信息
					cwh: targetItem.cwh,
					id: targetItem.id,
					isEntity: targetItem.isEntity
				};
				const originalOccupant = { ...targetItem };
				Object.assign(targetItem, this.dragState.person);
				Object.assign(targetItem, bedFields);
				const occupantForNotPlan = { ...originalOccupant };
				delete occupantForNotPlan.cwh;
				delete occupantForNotPlan.id;
				delete occupantForNotPlan.isEntity;
				this.roomData.prisoner.notPlan.splice(dragPersonIndex, 1, originalOccupant);
				console.log(this.roomData.prisoner.notPlan,'this.roomData.prisoner.notPlan');
			}
        }
      }
      else if (this.dragState.type === 'occupied') {// 从床位拖拽到床位（交换）
		console.log('occupied');
        [this.dragState.sourceItem.jgrybm, targetItem.jgrybm] = [targetItem.jgrybm, this.dragState.sourceItem.jgrybm];
          
        [this.dragState.sourceItem.jgryxm, targetItem.jgryxm] = [targetItem.jgryxm, this.dragState.sourceItem.jgryxm];
          
        [this.dragState.sourceItem.frontPhoto, targetItem.frontPhoto] = [targetItem.frontPhoto, this.dragState.sourceItem.frontPhoto];
        
		[this.dragState.sourceItem.enterDay, targetItem.enterDay] =  [targetItem.enterDay, this.dragState.sourceItem.enterDay];
		[this.dragState.sourceItem.isEntity, targetItem.isEntity] =  [targetItem.isEntity, this.dragState.sourceItem.isEntity];
		[this.dragState.sourceItem.isSick, targetItem.isSick] =  [targetItem.isSick, this.dragState.sourceItem.isSick];
		[this.dragState.sourceItem.riskLevel, targetItem.riskLevel] =  [targetItem.riskLevel, this.dragState.sourceItem.riskLevel];
		[this.dragState.sourceItem.riskLevelName, targetItem.riskLevelName] =  [targetItem.riskLevelName, this.dragState.sourceItem.riskLevelName];
      }
      
      this.dragState = null;
    },
	// 放置到背景（取消分配）
    onDrop(event) {
      event.preventDefault();
      
      if (this.dragState && this.dragState.type === 'occupied') {
        // 将人员移回待安排列表
        this.roomData.prisoner.notPlan.push({
          id: this.dragState.sourceItem.id,
          jgrybm: this.dragState.sourceItem.jgrybm,
          jgryxm: this.dragState.sourceItem.jgryxm,
          frontPhoto: this.dragState.sourceItem.frontPhoto,
		  enterDay: this.dragState.sourceItem.enterDay,
		  isEntity: this.dragState.sourceItem.isEntity,
		  isSick: this.dragState.sourceItem.isSick,
		  riskLevel: this.dragState.sourceItem.riskLevel,
		  riskLevelName: this.dragState.sourceItem.riskLevelName,
        });
        
        // 清空拖拽里的床位信息
		this.dragState.sourceItem.id = null;
        this.dragState.sourceItem.jgrybm = null;
        this.dragState.sourceItem.jgryxm = null;
        this.dragState.sourceItem.frontPhoto = '';
		this.dragState.sourceItem.enterDay = null;
        this.dragState.sourceItem.isEntity = null;
        this.dragState.sourceItem.isSick = '';
		this.dragState.sourceItem.riskLevel = null;
        this.dragState.sourceItem.riskLevelName = null;
        
        this.dragState = null;
      }
    },
	// Tooltip
    getTooltipContent(item) {
		// console.log(item,'tooltip');
	  if(!item.enterDay && item.riskLevelName) {
		return;
	  }
      
      let content = '';
      if (item.riskLevel) {
        content += `风险等级: ${this.getRiskName(item.riskLevel)}`;
      }
      content += `入监天数: ${item.enterDay || '-'}`;
      return content;
    },
  
  // 分配人员到床位
  assignPerson(person, bed, index) {
    const target = bed.bedList[index];
    
    // 如果目标床位有人，移到待安排
    if (target.jgrybm) {
      this.unassignedPersons.push({...target});
    }
    
    // 更新床位人员
    this.$set(bed.bedList, index, {...person});
    
    // 从待安排中移除
    this.unassignedPersons = this.unassignedPersons.filter(p => p.id !== person.id);
  },
  
  // 交换人员
  swapPersons(source, targetBed, targetIndex) {
    const sourceBed = this.bedLayouts.find(b => b.id === source.bedId);
    const sourceIndex = sourceBed.bedList.findIndex(item => item.id === source.item.id);
    const targetItem = targetBed.bedList[targetIndex];
    
    // 交换位置
    this.$set(sourceBed.bedList, sourceIndex, {...targetItem});
    this.$set(targetBed.bedList, targetIndex, {...source.item});
  },
	  
	  // 分配人员到床位
	  assignPersonToBed(person, bed) {
		// 从原床位移除（如果已分配）
		const prevBedIndex = this.bedLayouts.findIndex(b => b.cwhList && b.cwhList.id === person.id);
		if (prevBedIndex >= 0) {
		  this.bedLayouts[prevBedIndex].cwhList = null;
		}
		
		// 添加到新床位
		bed.cwhList = {
		  id: person.id,
		  name: person.name
		};
		
		// 从未分配列表移除
		// this.unassignedPersons = this.unassignedPersons.filter(p => p.id !== person.id);
	  },
	  
	  // 移除人员
	  removePerson(bed) {
		if (!bed.cwhList) return;
		
		// 添加到未分配列表
		this.unassignedPersons.push({
		  id: bed.cwhList.id,
		  name: bed.cwhList.name
		});
		
		// 从床位移除
		bed.cwhList = null;
	  },
	  
	  // 添加空床位
	  addNewBed(bed) {
		console.log(bed,'bed');
		if (!bed.isAllowedAdd) return;

		let maxNumber = 0;
  		let prefix = ''; // 不固定，先空着
		
		bed.bedList.forEach(i => {
			console.log(i.cwh);
			const cwh = i.cwh || '';
			const match = cwh.match(/^([A-Za-z]+)(\d+)$/);
			if (match) {
			const currentPrefix = match[1];
			const numberPart = parseInt(match[2], 10);
			if (numberPart > maxNumber) {
				maxNumber = numberPart;
				prefix = currentPrefix; // 更新为最大数字对应的字母部分
			}
			}
			// 这里要把i里面的床位号都拿出来 一百都是A100，B211这种格式 要实现的是 把床位号拿出来 然后对比出来那个数字最大 然后 在创建新的空床位的时候 空床位的cwh就不要随机生成了 要在拿到的最大的床位号的基础上加1，然后还要判断一下bed.addLocation的值 如果是left，top就不要用push追加了 是要到bed.bedList的最前面
		})
		if (!prefix) prefix = 'A';

  		const newCwh = `${prefix}${maxNumber + 1}`;
		// 创建新的空床位
		const newBed = {
			cwh: newCwh,
			isEntity: false,
			id: `new-${Date.now()}`,
			jgrybm: null,
			jgryxm: '',
			frontPhoto: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABwCAYAAACAVlfhAAAAAXNSR0IArs4c6QAABDBJREFUeF7t3F9uElEYBfDvwkChgpXWGFJr/F+jiemDPnQBXYG7cQWuRlfgAnzQB2OisWrUWJvG2CJ/Zwozc82dpEqt2ME5X4H08NjMHO78OBfuzBSMiEgQ2NVYwkdWZEOsPev+xkdKAWNaRuRpTryHpZLZNA4zsuEzEVtLGcHN/ipgGnnjrZteMHhsrX1ApewCxpgnphsMmpzm2TGTBDf9u37fguIY40wJiu0BQbGebCjYk6AERQuA8/geSlCwADiODSUoWAAcx4YSFCwAjmNDCQoWAMexoQQFC4Dj2FCCggXAcWwoQcEC4Dg2lKBgAXAcG0pQsAA4jg0lKFgAHMeGEhQsAI5jQwkKFgDHsaEEBQuA406kof0wlkanL1E8mX9FzeeM1CpFKXo5MN/ROHXQbhDK5nZ7YpgHh+xQV5ercqbkqaKqg77+0pTefqR6EGnD5+fycufSQtrN/2s7ddDn7/f+a2BaO92/sagVneQSFMxLUIJmE+CUz+Z3ZG+CEvSwQNpP+UI+lyy+vbwZizCMbHLSMIjiVPudioaemfPk5nJ1bMwDQYf6brst3f3wWNRTAXp75WzmMxh3RvZmq0VQJ3Dvek2MGW+q/ylnrZUXHxoEdQKoaZjm/Rr1XKNeualY2KMO8tSBuosT5WJe3Cd6Lvd7ii8vlo+dqmk22N7zf20Wxzb55Pf70aGLM6gXb2INffmpIfVzZVmsFhPISTwc7F67Lzs/fFm7ovtLIOpT3jVluI2TAD14zpMYizroMKC7ct/xB9IPrcRW9+p9zhgpekYq5cKJXKk/OM4TAW37A/m660snOH7hrdHgSsmTi0tlqZYLGvGHMlVB3drwy/eefGvuqx9Imie4sDAnl87PZ17z/uu5VEE/7HSS8+xpeixWinKtXlEbkhroTiOQrd2e2sCzBK8szUu9VsoSMXJfFdAwiuXV5+bE73SOOmp3B/Tu5QXxFJZxKqA7DV+2dn8vslWqkDF0Zaks9RrmhGJ4KCqgb7+2pO1P5hM9rXO17Mmti/jfTVQBffmxIYNId52ZFm7UdoW8kbWr+LMmFdA0FymygiD21zivJyjilRnKIChBcQKc8jjLJImgBAULgOPYUIKCBcBxM9lQ1A04lOXwjbyZBNUYdBbc4bM4jbGpL+w1Bn3qQIen1TRPeY2xqTQ0S4NmfV+Cgl9BghIULACOY0MJChYAx7GhBAULgOPYUIKCBcBxbChBwQLgODaUoGABcBwbSlCwADiODSUoWAAcx4YSFCwAjmNDCQoWAMexoQQFC4Dj2FCCggXAcWwoQcEC4Dg2lKBgAXAcG0pQsAA4znSDQVOsxX/PGTzQmYgzpmV6weCxtfbBTAx4ygdpjHligsCuRjZ8JmLxXx6fcgDs8Ewjb7z15AfoHGos4SMrssHpPyazm+YiT3PiPSyVzOZPEanYnwm7hbcAAAAASUVORK5CYII=',
			addTime: new Date().toISOString(),
			enterDay: null,
			isSick: false,
			fxdj: null,
			riskLevel: null
		};
		
		// 添加到床位列表
		if (bed.addLocation === 'left' || bed.addLocation === 'top') {
			bed.bedList.unshift(newBed);
		} else {
			bed.bedList.push(newBed);
		}
		
		// 更新滚动状态
		this.calculateScrollState(bed);
	},
	  // 保存安排
	  saveArrangement() {
		const assignedBeds = this.bedLayouts
		  .filter(bed => bed.cwhList)
		  .map(bed => ({
			bedId: bed.id,
			personId: bed.cwhList.id
		  }));
		
		this.$emit('submit', {
		  roomId: this.roomData.rowData.room_id,
		  assignments: assignedBeds
		});
		
		this.$emit('on_show_table');
		this.$Message.success('床位安排保存成功');
	  },
	  bedLayoutEvent(orgCode){
	    console.log('床位布局设置');
	  },
	  bedLayoutCahnge(orgCode){
		// this.formData.layoutRow = ''
		// this.formData.layoutColumn = ''
		console.log('床位布局配置');
		this.getByOrgCode(orgCode)
	  },
	  getByOrgCode(orgCode) {
		this.$store.dispatch('authGetRequest',{
			url: this.$path.bsp_pam_bed_getByOrgCode,
			params: {
				orgCode
			}
		}).then(res => {
			if(res.success) {
				this.orgBedList = res.data
				if(this.orgBedList.length == 1) {
					this.formData.layoutId = this.orgBedList[0].id;
      		this.activeItem = this.orgBedList[0].id;
					this.isCustom = true
				}
				this.$nextTick(() => {
					this.bedInformationModal = true
				})
			} else {
				this.$Modal.error({
					title: '温馨提示',
					conent: res.msg || '接口操作失败！'
				})
			}
		})
	  },
	  automaticbedEvent() {
		this.getCwglList()
		console.log('自动床位设置');
	  },
	  automaticbedChange(row){
		console.log('自动床位配置');
		this.$store.dispatch('authGetRequest',{
			url: this.$path.bsp_pam_bed_autoBedByConfig,
			params: {
				orgCode: row.org_code,
				roomId: row.room_code
			}
		}).then(res => {
			if(res.success) {
				console.log(res,'res')
				this.roomData.getByRoomId = res.data
				this.roomData.prisoner.notPlan = res.data.notPlan
				this.roomData.rowData.org_code = res.data.orgCode
				this.roomData.rowData.room_code = res.data.roomId
				this.roomBgImgUrl = res.data.layoutUrl
			} else {
				this.$Modal.error({
					title: '温馨提示',
					content: res.msg || '接口操作失败！'
				})
			}
		})
	  },
	  getCwglList() {
		this.$store.dispatch('authGetRequest',{
			url: `/bsp-com/static/dic/acp/ZD_CWGL_ZDCWPZ.js`
		}).then(res => {
			let arr = []
			let numTon = eval('(' + res + ')')
			arr = numTon()
			console.log(arr,'arr')
			this.cwglList = arr
			this.$nextTick(() => {
				this.automaticBedModal = true
			})
		})
	  },
	  lastBedArrangement(){
		console.log('上次床位安排');
	  },
	  setControlPersonnel(){
		console.log('设置夹控人员');
	  },
	  automaticBedCancel(){
		this.automaticBedModal = false
	  },
	  automaticBedOk(row){
		console.log(row,'提交');
		this.$store.dispatch('authPostRequest',{
			url: this.$path.bsp_pam_bed_update,
			params: {
				bedAutoConfig: this.vertical,
				orgCode: row.org_code,
				roomId: row.room_code
			}
		}).then(res => {
			if(res.success) {
				console.log(res);
				this.$nextTick(() => {
					this.automaticBedModal = false
				})
			} else{
				this.$Modal.error({
					title: '温馨提示',
					content: res.msg || '接口操作失败！'
				})
			}
		})
	  },
	  bedInformatioCancel(){
		console.log(11,'1')
		this.bedInformationModal = false
	  },
	  getBedInfo(item) {
		console.log(item,'床位');
		this.formData.layoutId = item.id
      	this.activeItem = item.id;
		if(item.id == '1922917238197960000') {
			this.isCustom = true
		} else {
			this.isCustom = false
		}
		
		// 这里可以继续处理其他逻辑
	  },
	  bedInformatioOk(){
		console.log('床位',this.formData,this.roomData)
		// return;
		if(this.roomData) {
			this.formData.orgCode = this.roomData.rowData.org_code
			this.formData.roomId = this.roomData.rowData.room_code
			if(this.roomData.getByRoomId && this.roomData.getByRoomId.id != '') {
				this.formData.id = this.roomData.getByRoomId.id
			}
		}
		console.log(this.formData,'formData')
		if(!this.formData.layoutId && !this.formData.layoutColumn && !this.formData.layoutRow) {
			this.$Modal.error({
				title: '温馨提示',
				content: '请选择床位布局类型'
			})
			return;
		}
		if(this.isCustom) {
			if(!this.formData.layoutRow || !this.formData.layoutColumn) {
				this.$Modal.error({
					title: '温馨提示',
					content: '请填写自定义布局的行和列!'
				})
				return;
			}
		}
		this.$store.dispatch('authPostRequest',{
			url: this.$path.bsp_pam_bed_createBedByConfig,
			params: this.formData
		}).then(res => {
			if(res.success) {
				console.log(res,'res');
				this.$nextTick(() => {
					this.bedInformationModal = false
					// this.$emit('onClose')
					this.$emit('init',this.roomData.rowData)
				})
			} else {
				this.$Modal.error({
					title: '温馨提示',
					content: res.msg || '操作失败！'
				})
			}
		})
	  },
	  bedRyInfonOk(){
		
	  },
	  bedRyInfonCancel(){
		this.bedRyInfonModal = false
	  },
	  getJgryInfo(bed){
		this.bedRyInfonModal = true
	  },
	  leftArrow(box) {
      // 你的方法逻辑
	  console.log('left');
      },
      rightArrow(box) {
      // 你的方法逻辑
	  console.log('right');
      },
	  topArrow(box) {
		console.log('top');
	  },
	  bottomArrow(box) {
		console.log('bottom');
	  },
	  handleMouseEnter(bedId) {
		this.isHover = true;
		this.hoverBedId = bedId;
	  },
	  handleMouseLeave() {
		this.isHover = false;
		this.hoverBedId = null;
	  },
	  getRiskName(level) {
		switch(level) {
		case '1':
			return '一级风险';
		case '2':
			return '二级风险';
		case '3':
			return '三级风险';
		default:
			return '重病号';
		}
	  },
	  closeRyInfos(item,bed) {
		console.log(item,bed,'要删除的11');
		// console.log(item.jgrybm);
		// if(item.jgrybm == '') {
		// 	console.log(123);
		// }
		// if(item && item.isEntity) {
		// 	this.$Modal.error({
		// 		title:'温馨提示',
		// 		content: '实体床位不允许删除!'
		// 	})
		// 	return;
		// }
		if(item && item.jgrybm !== '' && item.jgrybm !== null) {
			console.log(item.jgrybm);
			console.log('if');
			// console.log('1');
			// 如果当前床位有人员 点击closeRyInfo时要把这个人员信息搞到左边notPlan里面
			const exists = this.roomData.prisoner.notPlan.some(p => p.id === item.id);
			if (!exists) {
				this.roomData.prisoner.notPlan.unshift({ ...item }); // 深拷贝，避免引用问题
			}
			Object.keys(item).forEach(key => {
				// console.log(key,'key');
			// 这里清空人员相关字段，具体字段根据你的数据结构调整
			if (key !== 'id' && key !== 'cwh' && key !== 'isEntity') { // 保留床位相关字段
				item[key] = '';
				item.frontPhoto = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABwCAYAAACAVlfhAAAAAXNSR0IArs4c6QAABDBJREFUeF7t3F9uElEYBfDvwkChgpXWGFJr/F+jiemDPnQBXYG7cQWuRlfgAnzQB2OisWrUWJvG2CJ/Zwozc82dpEqt2ME5X4H08NjMHO78OBfuzBSMiEgQ2NVYwkdWZEOsPev+xkdKAWNaRuRpTryHpZLZNA4zsuEzEVtLGcHN/ipgGnnjrZteMHhsrX1ApewCxpgnphsMmpzm2TGTBDf9u37fguIY40wJiu0BQbGebCjYk6AERQuA8/geSlCwADiODSUoWAAcx4YSFCwAjmNDCQoWAMexoQQFC4Dj2FCCggXAcWwoQcEC4Dg2lKBgAXAcG0pQsAA4jg0lKFgAHMeGEhQsAI5jQwkKFgDHsaEEBQuA406kof0wlkanL1E8mX9FzeeM1CpFKXo5MN/ROHXQbhDK5nZ7YpgHh+xQV5ercqbkqaKqg77+0pTefqR6EGnD5+fycufSQtrN/2s7ddDn7/f+a2BaO92/sagVneQSFMxLUIJmE+CUz+Z3ZG+CEvSwQNpP+UI+lyy+vbwZizCMbHLSMIjiVPudioaemfPk5nJ1bMwDQYf6brst3f3wWNRTAXp75WzmMxh3RvZmq0VQJ3Dvek2MGW+q/ylnrZUXHxoEdQKoaZjm/Rr1XKNeualY2KMO8tSBuosT5WJe3Cd6Lvd7ii8vlo+dqmk22N7zf20Wxzb55Pf70aGLM6gXb2INffmpIfVzZVmsFhPISTwc7F67Lzs/fFm7ovtLIOpT3jVluI2TAD14zpMYizroMKC7ct/xB9IPrcRW9+p9zhgpekYq5cKJXKk/OM4TAW37A/m660snOH7hrdHgSsmTi0tlqZYLGvGHMlVB3drwy/eefGvuqx9Imie4sDAnl87PZ17z/uu5VEE/7HSS8+xpeixWinKtXlEbkhroTiOQrd2e2sCzBK8szUu9VsoSMXJfFdAwiuXV5+bE73SOOmp3B/Tu5QXxFJZxKqA7DV+2dn8vslWqkDF0Zaks9RrmhGJ4KCqgb7+2pO1P5hM9rXO17Mmti/jfTVQBffmxIYNId52ZFm7UdoW8kbWr+LMmFdA0FymygiD21zivJyjilRnKIChBcQKc8jjLJImgBAULgOPYUIKCBcBxM9lQ1A04lOXwjbyZBNUYdBbc4bM4jbGpL+w1Bn3qQIen1TRPeY2xqTQ0S4NmfV+Cgl9BghIULACOY0MJChYAx7GhBAULgOPYUIKCBcBxbChBwQLgODaUoGABcBwbSlCwADiODSUoWAAcx4YSFCwAjmNDCQoWAMexoQQFC4Dj2FCCggXAcWwoQcEC4Dg2lKBgAXAcG0pQsAA4znSDQVOsxX/PGTzQmYgzpmV6weCxtfbBTAx4ygdpjHligsCuRjZ8JmLxXx6fcgDs8Ewjb7z15AfoHGos4SMrssHpPyazm+YiT3PiPSyVzOZPEanYnwm7hbcAAAAASUVORK5CYII='
			}
			});
		} else{
			console.log('else');
			// console.log('2',item);
			// if(item && item.isEntity) {
			// 	this.$Modal.error({
			// 		title:'温馨提示',
			// 		content: '实体床位不允许删除!'
			// 	})
			// 	return;
			// }
			// 如果当前床位没有人员信息 点击closeRyInfo时要把这个人员信息从当前bed.bedList里面删除
			 const index = bed.bedList.findIndex(bedItem => bedItem === item);
			if (index !== -1) {
				bed.bedList.splice(index, 1);
			}
		}
	  },
	  closeRyInfo(item,bed) {
		console.log(item,bed,'要删除的');
		// console.log(item.jgrybm);
		// if(item.jgrybm == '') {
		// 	console.log(123);
		// }
		// if(item && item.isEntity) {
		// 	this.$Modal.error({
		// 		title:'温馨提示',
		// 		content: '实体床位不允许删除!'
		// 	})
		// 	return;
		// }
		if(item && item.jgrybm !== '' && item.jgrybm !== null) {
			console.log('if');
			// 如果当前床位有人员 点击closeRyInfo时要把这个人员信息搞到左边notPlan里面
			const exists = this.roomData.prisoner.notPlan.some(p => p.id === item.id);
			if (!exists) {
				this.roomData.prisoner.notPlan.unshift({ ...item }); // 深拷贝，避免引用问题
			}
			Object.keys(item).forEach(key => {
				// console.log(key,'key');
			// 这里清空人员相关字段，具体字段根据你的数据结构调整
			if (key !== 'id' && key !== 'cwh' && key !== 'isEntity') { // 保留床位相关字段
				item[key] = '';
				item.frontPhoto = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABwCAYAAACAVlfhAAAAAXNSR0IArs4c6QAABDBJREFUeF7t3F9uElEYBfDvwkChgpXWGFJr/F+jiemDPnQBXYG7cQWuRlfgAnzQB2OisWrUWJvG2CJ/Zwozc82dpEqt2ME5X4H08NjMHO78OBfuzBSMiEgQ2NVYwkdWZEOsPev+xkdKAWNaRuRpTryHpZLZNA4zsuEzEVtLGcHN/ipgGnnjrZteMHhsrX1ApewCxpgnphsMmpzm2TGTBDf9u37fguIY40wJiu0BQbGebCjYk6AERQuA8/geSlCwADiODSUoWAAcx4YSFCwAjmNDCQoWAMexoQQFC4Dj2FCCggXAcWwoQcEC4Dg2lKBgAXAcG0pQsAA4jg0lKFgAHMeGEhQsAI5jQwkKFgDHsaEEBQuA406kof0wlkanL1E8mX9FzeeM1CpFKXo5MN/ROHXQbhDK5nZ7YpgHh+xQV5ercqbkqaKqg77+0pTefqR6EGnD5+fycufSQtrN/2s7ddDn7/f+a2BaO92/sagVneQSFMxLUIJmE+CUz+Z3ZG+CEvSwQNpP+UI+lyy+vbwZizCMbHLSMIjiVPudioaemfPk5nJ1bMwDQYf6brst3f3wWNRTAXp75WzmMxh3RvZmq0VQJ3Dvek2MGW+q/ylnrZUXHxoEdQKoaZjm/Rr1XKNeualY2KMO8tSBuosT5WJe3Cd6Lvd7ii8vlo+dqmk22N7zf20Wxzb55Pf70aGLM6gXb2INffmpIfVzZVmsFhPISTwc7F67Lzs/fFm7ovtLIOpT3jVluI2TAD14zpMYizroMKC7ct/xB9IPrcRW9+p9zhgpekYq5cKJXKk/OM4TAW37A/m660snOH7hrdHgSsmTi0tlqZYLGvGHMlVB3drwy/eefGvuqx9Imie4sDAnl87PZ17z/uu5VEE/7HSS8+xpeixWinKtXlEbkhroTiOQrd2e2sCzBK8szUu9VsoSMXJfFdAwiuXV5+bE73SOOmp3B/Tu5QXxFJZxKqA7DV+2dn8vslWqkDF0Zaks9RrmhGJ4KCqgb7+2pO1P5hM9rXO17Mmti/jfTVQBffmxIYNId52ZFm7UdoW8kbWr+LMmFdA0FymygiD21zivJyjilRnKIChBcQKc8jjLJImgBAULgOPYUIKCBcBxM9lQ1A04lOXwjbyZBNUYdBbc4bM4jbGpL+w1Bn3qQIen1TRPeY2xqTQ0S4NmfV+Cgl9BghIULACOY0MJChYAx7GhBAULgOPYUIKCBcBxbChBwQLgODaUoGABcBwbSlCwADiODSUoWAAcx4YSFCwAjmNDCQoWAMexoQQFC4Dj2FCCggXAcWwoQcEC4Dg2lKBgAXAcG0pQsAA4znSDQVOsxX/PGTzQmYgzpmV6weCxtfbBTAx4ygdpjHligsCuRjZ8JmLxXx6fcgDs8Ewjb7z15AfoHGos4SMrssHpPyazm+YiT3PiPSyVzOZPEanYnwm7hbcAAAAASUVORK5CYII='
			} 
			// if(key == 'frontPhoto') {
			// 	item[key] = ''
			// }
			});
		} else{
			console.log('else',item);
			// if(item && item.isEntity) {
			// 	this.$Modal.error({
			// 		title:'温馨提示',
			// 		content: '实体床位不允许删除!'
			// 	})
			// 	return;
			// }
			// 如果当前床位没有人员信息 点击closeRyInfo时要把这个人员信息从当前bed.bedList里面删除
			 const index = bed.bedList.findIndex(bedItem => bedItem === item);
			if (index !== -1) {
				bed.bedList.splice(index, 1);
			}
		}
	  },
	  customizeClick() {
		// this.formData.layoutId = ''
		// this.activeItem = null;
		// this.isCustom = true
	  }
	}
  };
  </script>
  
  <style lang="less" scoped>
  .bed-arrangement-container {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
  }
  
  .header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px;
	border-bottom: 1px solid #e8e8e8;
  }
  
  .content {
	width: 100%;
	display: flex;
	flex: 1;
	overflow: hidden;
  }
  
  .unassigned-list {
	// width: 358px;
	width: 22%;
	padding: 0px 16px;
	border-right: 1px solid #e8e8e8;
	// overflow-y: auto;
  }
  
  .person-list {
	margin-top: 10px;
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
	overflow-y: auto;          /* 新增 */
  	// min-height: calc(100vh - 200px); /* 根据实际情况调整高度 */
	height: calc(~'100% - 100px');
	padding-bottom: 120px;
  }
  .personList{
	margin-top: 10px;
	padding: 8px;
	display: flex;
	flex-direction: column;
	align-items: center;
	height: calc(~'100% - 100px');
	padding-bottom: 120px;
  }
  .person-lists{
	// height: 58vh;
	height: calc(~'100% - 100px');
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 100px;
  }
  
  .person-item {
	width: 48%;
	height: 222px;
	padding: 8px;
	margin-bottom: 8px;
	border: 1px solid #d9d9d9;
	border-radius: 4px;
	cursor: move;
	background: #fafafa;
	transition: all 0.3s;
	img{
		width: 100%;
		height: 176px;
		background: #D8D8D8;
		border-radius: 4px 4px 4px 4px;
	}
	p{
		text-align: center;
		line-height: 20px;
		font-size: 16px;
		color: #00244A;
		font-weight: 600;
	}
  }
  .person-item:nth-of-type(2n + 1){
	// background: red;
	margin-right: 12px;
  }
  
  .person-item:hover {
	border-color: #1890ff;
	background: #e6f7ff;
  }
  .person-items{
	width: 100%;
	// height: 60px;
	background-color: #fff;
	margin-bottom: 10px;
	padding: 10px;
	border: 1px solid #C4CED8;
	border-radius: 4px;
	display: flex;
	justify-content: space-between;
	// .notPlanInfo{
	// 	display: flex;
	// 	justify-content: space-between;
	// 	width: 100%;
	// 	height: 60px;
	// 	background-color: red;
	// }
  }
  
.tab-outter{
	display: flex;
    width: 100%;
    height: 38px;
    background: #FFFFFF;
    border-radius: 6px 6px 6px 6px;
    border: 1px solid #C4CED8;
	.tab-cls {
		width: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #2390FF;
		cursor: pointer;
	}
	.tab-active {
		background: #EAF4FF;
		border-radius: 6px;
		border: 1px solid #2390FF;
	}
}
  
  .bed-layout {
	flex: 1;
	// width: 88%;
	padding: 15px;
	.action-btn-cont{
		width: 100%;
		height: 66px;
		display: flex;
		text-align: right;
		flex-direction: row-reverse;
		.actionBtns{
			font-size: 16px;
			font-family: MicrosoftYaHei, MicrosoftYaHei;
			color: #2390FF;
			text-align: center;
			line-height: 37px;
			padding: 0 8px;
			height: 38px;
			background: #FFFFFF;
			border-radius: 4px 4px 4px 4px;
			border: 1px solid #2390FF;
		}
		.actionBtn{
			// width: 140px;
			height: 38px;
			background: #FFFFFF;
			border-radius: 4px 4px 4px 4px;
			border: 1px solid #2390FF;
			display: flex;
			align-items: center;
			cursor: pointer;
			p{
				// width: 108px;
				// height: 21px;
				line-height: 38px;
				font-family: MicrosoftYaHei, MicrosoftYaHei;
				font-weight: normal;
				font-size: 16px;
				color: #2390FF;
				text-align: center;
				font-style: normal;
				text-transform: none;
				border-right: 1px solid #2390FF;
				padding: 0 8px;
			}
			img{
				width: 20px;
				height: 20px;
				margin: 0px 5px;
				// border: 2px solid #2390FF;
			}
		}
	}
  }
  
  .bed-content{
	width: 100%;
	height: 600px;
	overflow: auto;
	margin: 0 auto;
  }
  .room-background {
	position: relative;
	margin-left: 90px;
	// padding-bottom: 20px;
	// top: 20px;
	// left: 50px;
	// bottom: 20px;
	// right: 20px;
	// width: 1005px;
	// height: 592.5px;
	// padding: 28px;
	// height: 600px;
	// background: red;
	// background-size: auto;
	// background-repeat: no-repeat;
	// background-position: top left;
	// margin: 0 auto;
  }

  .room-backgrounds{
	width: 100%;
	height: calc(~'100% - 51px');
	background: url('../../assets/images/roomManage/围墙.png');
	background-size: 100% 100%;
	background-repeat: no-repeat;
	padding: 12px;
	.roomBed-content{
		width: 100%;
		height: 100%;
		padding: 13px 0px;
		overflow: auto;
		// background: #1890ff;
		.roomBed-grid{
			display: grid;
			gap: 25px;
			height: 100%;
		}
	}
  }
  
  .bed-area {
	position: absolute;
	// border: 2px dashed #1890ff;
	display: flex;
	justify-content: center;
	align-items: center;
  }
  
  .occupied-bed {
	// width: 116px;
	// height: 168px;
	// background: rgba(24, 144, 255, 0.2);
	// display: flex;
	// justify-content: center;
	// align-items: center;
	// position: relative;
  }
  
  .bed-bg{
	width: 116px;
	height: 168px;
	padding: 12px 8px;
	// border: 1px solid #e4eaf0;

	// background: url();
  }
  .bed-bg:hover{
	// border: 1px solid #1890ff;
  }
  .bed-bgs{
	width: 184px;
	height: 116px;
	padding: 8px 12px;
	border: 1px solid #e4eaf0;

	// background: url();
  }
  .bed-bgs:hover{
	border: 1px solid #1890ff;
  }
  .bed-ryInfo{
	position: relative;
	width: 100%;
	height: 100%;
	background: #FFFFFF;
	border-radius: 6px 6px 6px 6px;
	border: 1px solid #E4EAF0;
	display: flex;
	flex-direction: column;
	padding: 8px;
	p{
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: normal;
		font-size: 14px;
		color: #00244A;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}
	span{
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: normal;
		font-size: 12px;
		color: #8D99A5;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}
	.close{
		position: absolute;
		top: 5px;
		right: 3px;
		display: none;
	}
  }
  
  .bed-ryInfo:hover{
	border: 1px solid #1890ff;
	cursor: pointer;
	.close {
		display: block;
	}
  }
  .bed-ryInfos{
	width: 100%;
	height: 100%;
	background: #FFFFFF;
	border-radius: 6px 6px 6px 6px;
	border: 1px solid #E4EAF0;
	display: flex;
	// flex-direction: column;
	padding: 8px;
	// margin-left: 8px;
	p{
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: normal;
		font-size: 16px;
		color: #00244A;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}
	span{
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: normal;
		font-size: 16px;
		color: #8D99A5;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}
	.close{
		position: absolute;
		top: 5px;
		right: 3px;
		display: none;
	}
  }
  .bed-ryInfos:hover{
	border: 1px solid #1890ff;
	cursor: pointer;
	.close {
		display: block;
	}
  }

  .add-bedCont{
	width: 100px;
	height: 100px;
	border: 1px dashed #fff;
	display: flex;
	flex-direction: column;
	align-items: center;
	border-radius: 6px;
	padding-top: 23px;
	background-color: rgba(255, 255, 255, 0.3); /* 透明白，透明度30% */
	margin-bottom: 12px;
  }
  .add-bed-area {
	background: rgba(0, 0, 0, 0.05);
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	padding: 12px 8px;
  }
  
  .add-bed-btn {
	font-family: MicrosoftYaHei, MicrosoftYaHei;
	font-weight: normal;
	font-size: 16px;
	color: #FFFFFF;
	text-align: center;
	font-style: normal;
	text-transform: none;
  }
  
  .remove-icon {
	position: absolute;
	top: 5px;
	right: 5px;
	cursor: pointer;
	color: #ff4d4f;
  }
  
  .remove-icon:hover {
	color: #ff7875;
  }
  .sys-sub-title{
	margin: 10px 0px !important;
  }
  .tip{
	font-family: MicrosoftYaHei, MicrosoftYaHei;
	font-weight: normal;
	font-size: 16px;
	color: #8D99A5;
	text-align: left;
	font-style: normal;
	text-transform: none;
	margin-top: 8px;
  }
  .scroll-bar {
      display: none;
      color: #fff;
      position: absolute;
      top: 50%;
      width: 32px;
      height: 50px;
      transform: translateY(-50%);
      text-align: center;
      line-height: 50px;
      background: rgba(0, 0, 0, 0.4);
      cursor: pointer;
      z-index: 3;
    }
    .scroll-bar-left {
      left: 0;
      top: 50%;
      border-radius: 0px 4px 4px 0px;
    }
    .scroll-bar-right {
      right: 0;
      border-radius: 4px 0px 0px 4px;
    }
	.bedItem{
		// border: 1px solid #e4eaf0;
	}
	.bedItem:hover{
		// border: 1px solid #1890ff;
	}
	/* 确保Tooltip不会被隐藏 */
	/deep/.ivu-tooltip-popper {
		overflow: visible !important;
	}
	.tooltip-container {
	position: relative;
	z-index: 1;
	overflow: visible;
	width: 100%;
	height: 100%;
	}

	.room-background {
	position: relative;
	margin-left: 50px;
	overflow: hidden;
	width: 100%;
	// height: 100%;
	height: calc(~'100% - 40px');
	overflow: auto;
	}

	/* Tooltip样式（保留原样式基础上新增层级和定位） */
	/deep/.ivu-tooltip-popper {
		z-index: 999 !important;
		pointer-events: auto;
	}
	/deep/.ivu-modal-body{
		min-height: 230px !important;
		position: relative !important;
	}
	/deep/.select-use-modals {
		.ivu-modal-body{
			min-height: 670px !important;
		}
	}
	.orgBed{
		min-height: 139px;
		background-color: #edecec;
		padding: 8px 12px;
		display: flex;
		flex-direction: column;
		align-items: center;
		border: 1px solid #C4CED8;
		cursor: pointer;
		margin-bottom: 10px;
		margin-right: 10px;
		img{
			width: 60px;
			height: 60px;
			margin-bottom: 20px;
		}
		span{
			color: #00244A;
			font-size: 14px;
		}
	}
	.orgBed-class{
		// width: 130px;
		// width: 15%;
		// min-width: 130px;
		// height: 120px;
		background-color: #edecec;
		padding: 8px 12px;
		display: flex;
		flex-direction: column;
		align-items: center;
		border: 1px solid #C4CED8;
		cursor: pointer;
		margin-bottom: 10px;
		margin-right: 10px;
		img{
			width: 100%;
			height: 100px;
		}
		span{
			color: #00244A;
			font-size: 14px;
		}
	}
	.active-border{
		border: 1px solid #1890ff;
	}
	.bedSize{
		width: 70px;
		height: 36px;
		border-radius: 4px;
		border: 2px solid #b5bcc2;
	}
	.bedSize:focus{
		outline: none;
	}
	.dw {
		position: relative;
		top: -6px;
		left: 0px;
		display: inline-block;
		width: 35px;
		text-align: center;
		// line-height: 36px;
		height: 36px;
		border: 2px solid #1890ff;
		border-left: none;
		border-radius: 0px 4px 4px 0px;
	}
	.zl:focus{
		outline: none;
	}
	.bed-config{
		padding-top: 20px;
	}
	.scroll-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  width: 32px;
  height: 50px;
  background: rgba(0, 0, 0, 0.4);
  cursor: pointer;
  z-index: 3;
  border-radius: 4px;
  
  img {
    width: 20px;
    height: 20px;
  }
  
  .ivu-icon {
    color: #fff;
  }
}

.scroll-bar-left {
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 0 4px 4px 0;
}

.scroll-bar-right {
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 4px 0 0 4px;
}

.scroll-bar-top {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 32px;
  border-radius: 4px 4px 0 0;
}

.scroll-bar-bottom {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 32px;
  border-radius: 0 0 4px 4px;
}

/* 确保Tooltip不会被隐藏 */
/deep/.ivu-tooltip-popper {
  z-index: 9999 !important;
  pointer-events: auto;
}
.tabName{
	display: inline-block;
	width: 26px;
	height: 22px;
	// padding: 6px;
	text-align: center;
	border-radius: 4px 4px 4px 4px;
	font-size: 14px;
	color: #FFFFFF !important;
	// margin-right: 8px;
	position: relative;
	top: 5px;
	left: 3px;
}
.tabNames{
	display: inline-block;
	width: 26px;
	height: 22px;
	text-align: center;
	border-radius: 4px 4px 4px 4px;
	font-size: 14px;
	position: absolute;
	left: 3px;
	background-color: #7A7C86;
	color: #FFFFFF !important;
}
.radio-group-container {
  display: flex;
  flex-direction: column;
}
  </style>