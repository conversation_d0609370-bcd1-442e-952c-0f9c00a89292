<template>
  <!-- 基本信息卡片组 -->
  <div class="header-detail-container">
    <!-- 收信信息卡片 -->
    <div class="info-card">
      <h4 class="card-title">
        <i class="card-icon">📨</i>
        收信信息
      </h4>
      <div class="card-content">
        <div class="info-item">
          <span class="info-label">被监管人员</span>
          <span class="info-value">{{ jgryxm || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">监室号</span>
          <span class="info-value">{{ roomName || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">来信日期</span>
          <span class="info-value">{{ formData.sendDate || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">信件邮编</span>
          <span class="info-value">{{ formData.mailNo || '-' }}</span>
        </div>
      </div>
    </div>

    <!-- 送信人信息卡片 -->
    <div class="info-card">
      <h4 class="card-title">
        <i class="card-icon">👤</i>
        送信人信息
      </h4>
      <div class="card-content">
        <div class="info-item">
          <span class="info-label">姓名</span>
          <span class="info-value">{{ formData.sendMailUser || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">关系</span>
          <span class="info-value">{{ formData.relationName || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">地址</span>
          <span class="info-value">{{ formData.sendAddress || '-' }}</span>
        </div>
      </div>
    </div>

    <!-- 处理信息卡片 -->
    <div class="info-card">
      <h4 class="card-title">
        <i class="card-icon">⚙️</i>
        处理信息
      </h4>
      <div class="card-content">
        <div class="info-item">
          <span class="info-label">登记人</span>
          <span class="info-value">{{ formData.registerUserName || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">登记时间</span>
          <span class="info-value">{{ formData.registerTime || '-' }}</span>
        </div>
      </div>
    </div>

    <!-- 信件内容卡片 -->
    <div class="info-card content-card" v-if="formData.mailUrl">
      <h4 class="card-title">
        <i class="card-icon">📄</i>
        信件内容
      </h4>
      <div class="card-content">
        <div class="mail-content">
          <view-img
            class="mail-image"
            :isView="true"
            v-for="(ele,i) in JSON.parse(formData.mailUrl)"
            :key="i+'bucketName'"
            :bucketName="bucketName"
            :serviceMark="serviceMark"
            :objectName="ele.objectName"
            :fileName="ele.fileName"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import viewImg from "_c/main/components/viewImg/viewImg.vue"

export default {
  props: {
    formData: Object,
    jgryxm: {
      default: '',
      type: String
    },
    roomName: {
      default: '',
      type: String
    }
  },
  components: {viewImg},
  data() {
    return {
      showattUrl: true,
      defaultListsuper: [],
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName
    }
  }
}
</script>

<style scoped lang="less">
@import '~@/assets/style/variables.less';

.header-detail-container {
  display: flex;
  flex-direction: column;
  gap: @margin-md;
  height: 100%;
}

.info-card {
  background: @background-color-base;
  border-radius: @border-radius-lg;
  box-shadow: @box-shadow-light;
  border: 1px solid @border-color-light;
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    box-shadow: @box-shadow-medium;
    transform: translateY(-1px);
  }

  &.content-card {
    flex: 1; /* 信件内容卡片占用更多空间 */
  }

  .card-title {
    background: linear-gradient(135deg, @primary-color 0%, @primary-color-hover 100%);
    color: @background-color-base;
    margin: 0;
    padding: @padding-md @padding-lg;
    font-size: @font-size-md;
    font-weight: @font-weight-medium;
    display: flex;
    align-items: center;
    position: relative;

    .card-icon {
      margin-right: @margin-sm;
      font-size: @font-size-lg;
    }

    &::after {
      content: '';
      position: absolute;
      left: @padding-lg;
      bottom: -2px;
      width: 30px;
      height: 2px;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 1px;
    }
  }

  .card-content {
    padding: @padding-lg;
  }
}

.info-item {
  display: flex;
  align-items: center;
  padding: @padding-sm 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: rgba(43, 95, 217, 0.02);
    border-radius: @border-radius-sm;
    margin: 0 -@padding-xs;
    padding-left: @padding-xs;
    padding-right: @padding-xs;
  }

  .info-label {
    color: @text-color-secondary;
    font-size: @font-size-sm;
    font-weight: @font-weight-medium;
    min-width: 80px;
    flex-shrink: 0;
    position: relative;

    &::after {
      content: ':';
      margin-left: 2px;
      color: @text-color-placeholder;
    }
  }

  .info-value {
    color: @text-color-primary;
    font-size: @font-size-sm;
    flex: 1;
    margin-left: @margin-md;
    word-break: break-all;
    line-height: 1.4;

    &:empty::before {
      content: '-';
      color: @text-color-placeholder;
      font-style: italic;
    }
  }
}

.mail-content {
  display: flex;
  flex-wrap: wrap;
  gap: @margin-sm;

  .mail-image {
    border-radius: @border-radius-md;
    overflow: hidden;
    box-shadow: @box-shadow-light;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: @box-shadow-medium;
    }

    /deep/ img {
      width: 120px !important;
      height: 80px !important;
      object-fit: cover;
      border-radius: @border-radius-md;
    }
  }
}

/* 响应式设计 */
@media (max-width: @screen-md) {
  .header-detail-container {
    gap: @margin-sm;
  }

  .info-card {
    .card-title {
      padding: @padding-xs @padding-sm;
      font-size: 11px;
    }

    .card-content {
      padding: @padding-sm;
    }
  }

  .info-item {
    padding: 4px 0;

    .info-label,
    .info-value {
      font-size: 11px;
    }
  }

  .mail-content {
    .mail-image /deep/ img {
      width: 80px !important;
      height: 60px !important;
    }
  }
}

@media (max-width: @screen-sm) {
  .info-item {
    flex-direction: column;
    align-items: flex-start;

    .info-label {
      margin-bottom: 2px;
    }

    .info-value {
      text-align: left;
      margin-left: 0;
    }
  }
}
</style>
