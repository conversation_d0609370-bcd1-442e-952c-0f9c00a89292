<template>
  <!-- 详情页面 -->
  <div class="header-detail-container">
    <!-- 基本信息区域 -->
    <div class="info-section">
      <h3 class="section-title">
        <i class="section-icon"></i>
        基本信息
      </h3>
      <div class="info-grid">
        <div class="info-item">
          <label class="info-label">被监管人员</label>
          <div class="info-value">{{ jgryxm || '-' }}</div>
        </div>
        <div class="info-item">
          <label class="info-label">监室号</label>
          <div class="info-value">{{ roomName || '-' }}</div>
        </div>
        <div class="info-item">
          <label class="info-label">来信日期</label>
          <div class="info-value">{{ formData.sendDate || '-' }}</div>
        </div>
        <div class="info-item">
          <label class="info-label">送信人姓名</label>
          <div class="info-value">{{ formData.sendMailUser || '-' }}</div>
        </div>
        <div class="info-item">
          <label class="info-label">关系</label>
          <div class="info-value">{{ formData.relationName || '-' }}</div>
        </div>
        <div class="info-item">
          <label class="info-label">来信地址</label>
          <div class="info-value">{{ formData.sendAddress || '-' }}</div>
        </div>
        <div class="info-item">
          <label class="info-label">信件邮编</label>
          <div class="info-value">{{ formData.mailNo || '-' }}</div>
        </div>
        <div class="info-item">
          <label class="info-label">登记人</label>
          <div class="info-value">{{ formData.registerUserName || '-' }}</div>
        </div>
        <div class="info-item">
          <label class="info-label">登记时间</label>
          <div class="info-value">{{ formData.registerTime || '-' }}</div>
        </div>
      </div>
    </div>

    <!-- 信件内容区域 -->
    <div class="content-section" v-if="formData.mailUrl">
      <h3 class="section-title">
        <i class="section-icon"></i>
        信件内容
      </h3>
      <div class="mail-content">
        <view-img
          class="mail-image"
          :isView="true"
          v-for="(ele,i) in JSON.parse(formData.mailUrl)"
          :key="i+'bucketName'"
          :bucketName="bucketName"
          :serviceMark="serviceMark"
          :objectName="ele.objectName"
          :fileName="ele.fileName"
        />
      </div>
    </div>
  </div>
</template>

<script>

import viewImg from "_c/main/components/viewImg/viewImg.vue"

export default {
  props: {
    formData: Object,
    jgryxm: {
      default: '',
      type: String
    },
    roomName: {
      default: '',
      type: String
    }
  },
  components: {viewImg},
  data() {
    return {
      showattUrl: true,
      defaultListsuper: [],
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName
    }
  }
}
</script>

<style scoped lang="less">
@import '~@/assets/style/variables.less';

.header-detail-container {
  display: flex;
  flex-direction: column;
  gap: @margin-lg;
}

.info-section,
.content-section {
  .section-title {
    display: flex;
    align-items: center;
    margin: 0 0 @margin-md 0;
    padding: @padding-sm @padding-md;
    background: linear-gradient(135deg, @primary-color 0%, @primary-color-hover 100%);
    color: @background-color-base;
    border-radius: @border-radius-md;
    font-size: @font-size-md;
    font-weight: @font-weight-medium;
    position: relative;

    .section-icon {
      width: 4px;
      height: 16px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 2px;
      margin-right: @margin-sm;
    }

    &::after {
      content: '';
      position: absolute;
      left: @padding-md;
      bottom: -3px;
      width: 40px;
      height: 2px;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 1px;
    }
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: @margin-md;

  .info-item {
    display: flex;
    align-items: center;
    background: @background-color-base;
    border: 1px solid @border-color-light;
    border-radius: @border-radius-md;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      border-color: @primary-color;
      box-shadow: 0 2px 8px rgba(43, 95, 217, 0.15);
    }

    .info-label {
      background: linear-gradient(135deg, @background-color-light 0%, #e8f2ff 100%);
      color: @text-color-primary;
      padding: @padding-md;
      font-weight: @font-weight-medium;
      font-size: @font-size-sm;
      min-width: 100px;
      text-align: center;
      border-right: 1px solid @border-color-light;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        right: 0;
        top: 20%;
        bottom: 20%;
        width: 1px;
        background: linear-gradient(to bottom, transparent, @primary-color, transparent);
      }
    }

    .info-value {
      flex: 1;
      padding: @padding-md;
      color: @text-color-secondary;
      font-size: @font-size-sm;
      background: @background-color-base;
      min-height: 48px;
      display: flex;
      align-items: center;

      &:empty::before {
        content: '-';
        color: @text-color-placeholder;
      }
    }
  }
}

.content-section {
  .mail-content {
    display: flex;
    flex-wrap: wrap;
    gap: @margin-md;
    padding: @padding-md;
    background: @background-color-light;
    border-radius: @border-radius-md;
    border: 1px solid @border-color-light;

    .mail-image {
      border-radius: @border-radius-md;
      overflow: hidden;
      box-shadow: @box-shadow-light;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: @box-shadow-medium;
      }

      /deep/ img {
        width: 180px !important;
        height: 120px !important;
        object-fit: cover;
        border-radius: @border-radius-md;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: @screen-md) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .info-item {
    .info-label {
      min-width: 80px;
    }
  }

  .mail-content {
    .mail-image /deep/ img {
      width: 150px !important;
      height: 100px !important;
    }
  }
}

@media (max-width: @screen-sm) {
  .info-item {
    flex-direction: column;

    .info-label {
      width: 100%;
      min-width: auto;
      border-right: none;
      border-bottom: 1px solid @border-color-light;

      &::after {
        display: none;
      }
    }
  }
}
</style>
