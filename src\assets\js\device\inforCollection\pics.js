var svrPath = "http://192.168.3.219:8000/automationsvr/v1/";
var gcontentType = "application/x-www-form-urlencoded";
var syncFlag = false;
//是否为空串
function IsNull(value) {
	//正则表达式用于判斷字符串是否全部由空格或换行符组成
	var reg = /^\s*$/;
	var flag = (value != null && value != undefined && !reg.test(value));
	return !flag;
}
function isDate(s) {
	// 检查字符串是否匹配日期格式：yyyymmdd
	if (!/^(\d{4})(\d{2})(\d{2})$/.test(s)) {
		return false;
	}

	// 创建 Date 对象并从字符串中提取年、月、日
	const d = new Date(s.substring(0, 4), parseInt(s.substring(4, 6)) - 1, s.substring(6, 8));

	// 检查 Date 对象中提取的年、月、日是否与字符串中的匹配
	return d.getFullYear() == s.substring(0, 4) &&
		d.getMonth() + 1 == s.substring(4, 6) &&
		d.getDate() == s.substring(6, 8);
}

function isTime(str) {
	//yyyyMMddHHmmss
	//0001 01 01 00 00 00
	//00010101000000

	var validate = /^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})(((0[13578]|1[02])(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)(0[1-9]|[12][0-9]|30))|(02(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))0229))([0-1]?[0-9]|2[0-3])([0-5][0-9])([0-5][0-9])$/;
	var reg = new RegExp(validate);

	if (str == null || str == undefined || str == '')
		return false;

	if (!reg.test(str))
		return false;

	return true;
}
function yyyyMMddHHmmss(str) {
	//yyyyMMdd HH:mm:ss
	if (str == null || str == undefined || str == '')
		return false;

	if (str.length != 14)
		return false;

	return isTime(str);
}
// 获取当前时间
function getCurrentTime() {
	var date = new Date();
	var year = date.getFullYear();
	var month = date.getMonth() + 1;
	var day = date.getDate();
	var hour = date.getHours();
	var minute = date.getMinutes();
	var second = date.getSeconds();
	month = month < 10 ? "0" + month : month;
	day = day < 10 ? "0" + day : day;
	hour = hour < 10 ? "0" + hour : hour;
	minute = minute < 10 ? "0" + minute : minute;
	second = second < 10 ? "0" + second : second;
	return year + month + day + hour + minute + second;
}

function getBirthdateFromIdCard(idCard) {
	// 正则表达式匹配身份证号码中的出生日期部分
	const birthdateRegex = /^(\d{6})(\d{4})(\d{2})(\d{2})/;

	// 匹配身份证号码
	const match = idCard.match(birthdateRegex);

	// 如果匹配成功，则提取出生日期部分
	if (match) {
		const year = match[2];
		const month = match[3];
		const day = match[4];

		// 返回出生日期，注意身份证中的月份和日期如果是单个数字，前面没有0
		return `${year}${month}${day}`;
	} else {
		// 如果匹配不成功，则返回 null
		return null;
	}
}


function putKV(key, value) {
	localStorage.setItem(key, value);
}

function getKV(key) {
	return localStorage.getItem(key);
}

function formatTimestamp(timestamp) {
	// 将时间戳转换为字符串
	const timestampString = timestamp.toString();

	// 提取年月日时分秒
	const year = timestampString.substring(0, 4);
	const month = timestampString.substring(4, 6);
	const day = timestampString.substring(6, 8);
	const hour = timestampString.substring(8, 10);
	const minute = timestampString.substring(10, 12);
	const second = timestampString.substring(12, 14);

	// 格式化时间
	return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
}


var HisignPICS = {

	//检测服务是否正常
	CheckCollectSvr: function () {
		var rs = {};
		var param = { "autoSvrID": "GDZC", "methodName": "CheckCollectSvr" };
		jQuery.support.cors = true;
		$.ajax({
			type: "post",
			url: svrPath,
			timeout: 2000,
			async: false,
			data: param,
			contentType: gcontentType,
			dataType: "json",
			success: function (data) {
				rs = eval(data);
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				rs.code = -1;
				rs.msg = "检测一体化采集服务失败!";
			}
		});
		return rs;
	},

	//设置采集模式
	SetSyncCollect: function (flag) {
		syncFlag = flag;
	},

	//启动8.0采集
	StartCollect: function (ryxx, backUrl, isTest) {
		var rs = {};
		ryxx["autoSvrID"] = "GDZC";
		ryxx["methodName"] = "StartCollect";
		ryxx["backUrl"] = backUrl;
		ryxx["isTest"] = isTest.toString();
		if (syncFlag) {
			ryxx["waitFlag"] = "true";
		}
		jQuery.support.cors = true;
		$.ajax({
			type: "post",
			url: svrPath,
			timeout: 5000,
			async: false,
			data: ryxx,
			contentType: gcontentType,
			dataType: "json",
			success: function (data) {
				rs = eval(data);
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				rs.code = -1;
				rs.msg = "启动一体化采集失败,请检查一体化服务是否正常!";
			}
		});
		return rs;
	},

	//获取数据
	GetCollectData: function (rybh) {
		var rs = {};
		if (IsNull(rybh)) {
			rs.code = -1;
			rs.msg = "入所编号不能为空!";
			return rs;
		}
		var realRYBH = getKV(rybh);
		if (IsNull(realRYBH)) {
			rs.code = -1;
			rs.msg = "入所编号在综采系统不存在!";
			return rs;
		}
		var param = { "autoSvrID": "GDZC", "methodName": "GetCollectData", "RYBH": realRYBH };
		jQuery.support.cors = true;
		$.ajax({
			type: "post",
			url: svrPath,
			timeout: 5000,
			async: false,
			data: param,
			contentType: gcontentType,
			dataType: "json",
			success: function (data) {
				rs = eval(data);
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				rs.code = -1;
				rs.msg = "获取采集数据失败,请检查一体化服务是否正常!";
			}
		});
		rs.data.zwbh = realRYBH;
		console.error('返回的数据')
		console.error(rs)
		return rs;
	},

	//基本信息修改
	UpdateData: function (ryxx) {
		var rs = {};
		ryxx["autoSvrID"] = "GDZC";
		ryxx["methodName"] = "UpdateData";
		jQuery.support.cors = true;
		$.ajax({
			type: "post",
			url: svrPath,
			timeout: 5000,
			async: false,
			data: ryxx,
			contentType: gcontentType,
			dataType: "json",
			success: function (data) {
				rs = eval(data);
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				rs.code = -1;
				rs.msg = "基本信息修改失败,请检查一体化服务是否正常!";
			}
		});
		return rs;
	},
	//读取身份证信息
	ReadIDCard: function () {
		var rs = {};
		var ryxx = {};
		ryxx["autoSvrID"] = "GDZC";
		ryxx["methodName"] = "ReadICCardInfo";
		jQuery.support.cors = true;
		$.ajax({
			type: "post",
			url: svrPath,
			timeout: 5000,
			async: false,
			data: ryxx,
			contentType: gcontentType,
			dataType: "json",
			success: function (data) {
				rs = eval(data);
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				rs.code = -1;
				rs.msg = "读取身份证信息失败,请检查一体化服务是否正常!";
			}
		});
		return rs;
	},
	//获取设备编号
	GetDeviceNo: function () {
		var rs = {};
		var param = { "autoSvrID": "GDZC", "methodName": "GetDeviceNo" };
		jQuery.support.cors = true;
		$.ajax({
			type: "post",
			url: svrPath,
			timeout: 5000,
			async: false,
			data: param,
			contentType: gcontentType,
			dataType: "json",
			success: function (data) {
				rs = eval(data);
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				rs.code = -1;
				rs.msg = "获取设备注册编号失败,请检查一体化服务是否正常!";
			}
		});
		return rs;
	},
	//提交数据
	CommitData: function (rybh) {
		var rs = {};
		if (IsNull(rybh)) {
			rs.code = -1;
			rs.msg = "入所编号不能为空!";
			return rs;
		}
		var realRYBH = getKV(rybh);
		if (IsNull(realRYBH)) {
			rs.code = -1;
			rs.msg = "入所编号在综采系统不存在!";
			return rs;
		}
		var param = { "autoSvrID": "GDZC", "methodName": "CommitData", "RYBH": realRYBH };
		jQuery.support.cors = true;
		$.ajax({
			type: "post",
			url: svrPath,
			timeout: 5000,
			async: false,
			data: param,
			contentType: gcontentType,
			dataType: "json",
			success: function (data) {
				rs = eval(data);
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				rs.code = -1;
				rs.msg = "提交数据失败,请检查一体化服务是否正常!";
			}
		});
		return rs;
	},

	// mock基本信息数据
	MockBasicInfo(info) {
		var mockData = {};
		mockData.XM = '无';
		mockData.XBDM = '2';
		mockData.CSRQ = '19000101';
		mockData.GJDM = '156';
		mockData.MZDM = '01';
		mockData.HJD_XZQHDM = '320000';
		mockData.HJD_DZMC = '无';
		mockData.XZD_XZQHDM = '220203';
		mockData.XZD_DZMC = '无';
		mockData.CJR_XM = '无';
		mockData.CJR_SFHM = '110101199003079913';
		mockData.CJR_JH = '999999';
		mockData.BCJRYLBDM = '08';
		mockData.CJDW_GAJGJGDM = '320200000000';
		mockData.CJDW_DWMC = '无锡市公安局';
		mockData.CJSJ = getCurrentTime();

		// 如果警综必填数据为空,则替换为mock数据
		for (let i in mockData) {
			if (!info[i] || info[i] == null || info[i] == undefined) {
				info[i] = mockData[i]
			}
		}
		console.log("警综数据:", JSON.stringify(info))
		console.log("MOCK数据:", JSON.stringify(mockData))
		console.error(JSON.stringify(info))
		return info;
	},


	//保存基本信息
	SaveBaseInfo: function (ryxx) {
		var rs = {};

		if (IsNull(ryxx.XM)) {
			rs.code = -1;
			rs.msg = "姓名不能为空!";
			return rs;
		}
		if (IsNull(ryxx.XBDM)) {
			rs.code = -1;
			rs.msg = "性别代码不能为空!";
			return rs;
		}
		if (IsNull(ryxx.CSRQ)) {
			rs.code = -1;
			rs.msg = "出生日期不能为空!";
			return rs;
		}
		if (!isDate(ryxx.CSRQ)) {
			rs.code = -1;
			rs.msg = "出生日期不是合法的日期:" + ryxx.CSRQ;
			return rs;
		}
		if (IsNull(ryxx.GJDM)) {
			rs.code = -1;
			rs.msg = "国籍代码不能为空!";
			return rs;
		}
		if (IsNull(ryxx.HJD_XZQHDM)) {
			rs.code = -1;
			rs.msg = "户籍地行政区划代码不能为空!";
			return rs;
		}
		if (IsNull(ryxx.HJD_DZMC)) {
			rs.code = -1;
			rs.msg = "户籍地详址不能为空!";
			return rs;
		}
		if (IsNull(ryxx.XZD_XZQHDM)) {
			rs.code = -1;
			rs.msg = "现住地行政区划代码不能为空!";
			return rs;
		}
		if (IsNull(ryxx.XZD_DZMC)) {
			rs.code = -1;
			rs.msg = "现住地详址不能为空!";
			return rs;
		}
		if (IsNull(ryxx.XZD_DZMC)) {
			rs.code = -1;
			rs.msg = "现住地详址不能为空!";
			return rs;
		}
		if (IsNull(ryxx.BCJRYLBDM)) {
			rs.code = -1;
			rs.msg = "被采集人员类别代码不能为空!";
			return rs;
		}
		if (IsNull(ryxx.CJR_XM)) {
			rs.code = -1;
			rs.msg = "采集人姓名不能为空!";
			return rs;
		}
		if (IsNull(ryxx.CJR_SFHM)) {
			rs.code = -1;
			rs.msg = "采集人身份证号码不能为空!";
			return rs;
		}
		if (IsNull(ryxx.CJDW_GAJGJGDM)) {
			rs.code = -1;
			rs.msg = "采集单位代码不能为空!";
			return rs;
		}
		if (IsNull(ryxx.CJDW_DWMC)) {
			rs.code = -1;
			rs.msg = "采集单位名称不能为空!";
			return rs;
		}
		if (IsNull(ryxx.CJSJ)) {
			rs.code = -1;
			rs.msg = "采集时间不能为空!";
			return rs;
		}
		if (!isTime(ryxx.CJSJ)) {
			rs.code = -1;
			rs.msg = "采集时间格式不正确,应为yyyyMMddHHmmss格式!";
			return rs;
		}

		ryxx.CJSJ = formatTimestamp(ryxx.CJSJ);
		ryxx["autoSvrID"] = "GDZC";
		ryxx["methodName"] = "SaveBaseInfo";
		console.error(ryxx)
		jQuery.support.cors = true;
		$.ajax({
			type: "post",
			url: svrPath,
			timeout: 5000,
			async: false,
			data: ryxx,
			contentType: gcontentType,
			dataType: "json",
			success: function (data) {
				rs = eval(data);
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				rs.code = -1;
				rs.msg = "保存人员基本信息失败,请检查一体化服务是否正常!";
			}
		});

		return rs;
	},
	//单项采集
	StartItemCollect: function (rybh, info, itemName, callback) {
		var rs = {};

		if (IsNull(rybh)) {
			rs.code = -1;
			rs.msg = "入所编号不能为空!";
			return rs;
		}
		if (IsNull(info)) {
			rs.code = -1;
			rs.msg = "人员基本信息不能为空!";
			return rs;
		}
		if (IsNull(itemName)) {
			rs.code = -1;
			rs.msg = "采集项名称不能为空!";
			return rs;
		}

		if (typeof info == "string") {
			info = JSON.parse(info)
		}

		// 人员采集编号
		if ('RYBH' in info) {
			// 属性存在，可以安全地删除
			delete info.RYBH;
		}

		console.error("警综来源数据:", JSON.stringify(info))
		console.error("警综来源数据编号:", rybh)

		info.FWCS = info.JZRYBH;
		info.JZRYBH = rybh;
		info.CSD_DZMC = info.BJTYRYBH;
		//判断身份证号码是否为空
		if (!IsNull(info.GMSFHM)) {
			info.CSRQ = getBirthdateFromIdCard(info.GMSFHM);
			if (IsNull(info.CSRQ)) {
				info.CSRQ = "19000101";
			}
		}

		// 保存基本信息
		var saveBaseInfoRes = this.SaveBaseInfo(this.MockBasicInfo(info));
		console.error(saveBaseInfoRes)
		// 基本信息保存失败，阻断采集项调用
		if (saveBaseInfoRes.code < 0) {
			console.error(saveBaseInfoRes)
			return saveBaseInfoRes
		}
		console.log("保存基本信息成功",saveBaseInfoRes)
		//拿到生成的人员编号
		var realRYBH = saveBaseInfoRes.data.rybh;
		//保存对应关系
		putKV(rybh, realRYBH);
		var allItems = ["finger", "photo", "dna", "voice", "iris", "foot", "effects", "handwriting", "bankCard", "phone", "drug", "nFinger", "gait", "sim", "computer", "marker", "printDna"];
		var bOK = false;
		for (var each in allItems) {
			if (allItems[each] == itemName) {
				bOK = true;
				break;
			}
		}
		if (!bOK) {
			rs.code = -1;
			rs.msg = "采集项名称不合法:" + itemName;
			return rs;
		}
		var param = { "autoSvrID": "GDZC", "methodName": "StartItemCollect", "RYBH": realRYBH, "param1": itemName };

		param["waitFlag"] = "true";

		jQuery.support.cors = true;
		$.ajax({
			type: "post",
			url: svrPath,
			timeout: 0,
			async: true,
			data: param,
			contentType: gcontentType,
			dataType: "json",
			success: function (data) {
				rs = eval(data);
				console.log(data)
				if (data.code == 1) {
					callback(HisignPICS.GetCollectData(rybh))
				} else {
					callback(rs)
				}
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				rs.code = -1;
				rs.msg = "启动单项采集失败,请检查一体化服务是否正常!";
				callback(rs)
			}
		});
		rs.code = 1;
		rs.msg = "单项采集方法调用成功!";
		return rs;
	},
	// 增加打印指纹卡方法
	PrintFPCard: function (rybh) {
		var ws = new WebSocket("ws://127.0.0.1:9000/");
		ws.onopen = function () {
			ws.send("#$@%TOKEN=^&*"); //将消息发送到服务端
		}
		ws.onmessage = function (e) {
			//当客户端收到服务端发来的消息时，触发onmessage事件，参数e.data包含server传递过来的数据
			var respStr = e.data;
			var resp = JSON.parse(respStr);
			if (resp.code == 1) {
				var token = resp.data;
				var keystr = "/adapter/";
				var prefixIndex = window.location.href.indexOf(keystr);
				var printUrl = window.location.href.substr(0, prefixIndex) + "/applicationFunction/printZwk.html?rybh=" + rybh + "&token=" + token;
				//这部分可能需要加token
				window.location.href = printUrl;
			} else {
				$("#disText").text("获取token失败,无法打印指纹卡！");
			}
			ws.close();
		}
		ws.onclose = function (e) {
			//$("#disText").text("websocket连接已关闭!");
			ws.close();
		}
		ws.onerror = function (e) {
			$("#disText").text("无法连接websocket,请确认人员采集服务是否在运行!");
			ws.close();
		}
	},
	// 打印dna
	PrintDna: function (rybh) {
		var rs = {};

		if (!rybh) {
			rs.code = -1;
			rs.msg = "启动DNA打印失败,缺少入所编号！";
			return rs;
		}

		var realRYBH = getKV(rybh);
		if (IsNull(realRYBH)) {
			rs.code = -1;
			rs.msg = "入所编号在综采系统不存在!";
			return rs;
		}

		var printDna = 'printDna' + "?rybh=" + realRYBH;
		var param = { "autoSvrID": "GDZC", "methodName": "StartItemCollect", "RYBH": realRYBH, "param1": printDna };
		// 同步异步标识
		param["waitFlag"] = "true";
		jQuery.support.cors = true;
		$.ajax({
			type: "post",
			url: svrPath,
			timeout: 0,
			async: true,
			data: param,
			contentType: gcontentType,
			dataType: "json",
			success: function (data) {
				rs = eval(data);
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				rs.code = -1;
				rs.msg = "启动DNA打印失败,请检查一体化服务是否正常!";
			}
		});
		return rs;

	},
	// 打印指纹卡
	PrintLocalFPCard: function (rybh) {
		var rs = {}
		if (!rybh) {
			rs.code = -1;
			rs.msg = "启动指纹卡打印失败,缺少入所编号！";
			return rs;
		}

		var realRYBH = getKV(rybh);
		if (IsNull(realRYBH)) {
			rs.code = -1;
			rs.msg = "入所编号在综采系统不存在!";
			return rs;
		}

		var printFPCard = 'printFPCard' + "?rybh=" + realRYBH;;
		var param = { "autoSvrID": "GDZC", "methodName": "StartItemCollect", "RYBH": realRYBH, "param1": printFPCard };
		jQuery.support.cors = true;
		$.ajax({
			type: "post",
			url: svrPath,
			timeout: 5000,
			async: false,
			data: param,
			contentType: gcontentType,
			dataType: "json",
			success: function (data) {
				rs = eval(data);
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				rs.code = -1;
				rs.msg = "启动指纹卡打印失败,请检查一体化服务是否正常!";
			}
		});
		return rs;

	}
};

// 默认导出
export default HisignPICS;
export { getCurrentTime };





