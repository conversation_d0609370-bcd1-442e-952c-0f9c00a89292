<template>
  <!-- 详情页面 -->
  <div class="detail-wrap">
    <div class="bsp-base-form">
      <div class="bsp-base-tit">
        {{ editTitle }}
      </div>
      <div class="prison-select-center">
        <div class="prison-select-center-top">
          <div class="prison-select-center-top-ryxx">
            <personnel-selector
              :value="jgrybm"
              mode="detail"
              title="被监管人员"
              :show-case-info="true"
            />
          </div>
          <div class="prison-select-center-top-detail">
            <headerDetail :formData="formData" :jgryxm="jgryxm" :roomName="roomName"/>
          </div>
        </div>
        <div class="prison-select-center-bottom">
          <div class="timeline-container">
            <p class="sys-sub-title">流程轨迹</p>
            <div class="timeline-wrapper">
              <record :formData="formData" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bsp-base-fotter">
      <Button style="margin: 0 20px" @click="handleClose()">返 回</Button>
    </div>
  </div>
</template>

<script>

import viewImg from "_c/main/components/viewImg/viewImg.vue"
import personnelSelector from "@/components/personnel-selector"
import headerDetail from "./headerDetail.vue"
import record from "./record.vue"
export default {
  props: {
    jgryxm: {
      default: '',
      type: String
    },
    jgrybm: {
      default: '',
      type: String
    },
    roomName: {
      default: '',
      type: String
    },
    curId: {
      default: '',
      type: String
    }
  },
  components: {headerDetail, viewImg, record, personnelSelector},
  data() {
    return {
      showattUrl: true,
      formData: {},
      defaultListsuper: [],
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName,
      editTitle: '收信信息'
    }
  },
  methods: {
    handleClose() {
      this.$emit('close', false)
    },
    getData() {
      this.$store.dispatch("authGetRequest", {
        url: this.$path.familyContact_get,
        params: {
          id: this.curId
        }
      }).then(res => {
        if (res.success) {
          this.formData = res.data
          this.formData.jgryxm = this.jgryxm
          this.formData.roomName = this.roomName
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '操作失败'
          })
        }
      })
    }
  },
  mounted() {
    this.getData()
  }
}
</script>
<style scoped lang="less">
@import '~@/assets/style/variables.less';

/* 重置 bsp-base-form 的绝对定位，避免布局问题 */
.detail-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;

  .bsp-base-form {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .bsp-base-tit {
    flex-shrink: 0;
  }

  .bsp-base-fotter {
    position: relative !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    flex-shrink: 0;
    margin-top: auto;
  }
}

.prison-select-center-top-ryxx {
  width: 30%;
  margin-right: @margin-md;
}

.prison-select-center-top-detail {
  width: 70%;
}

.prison-select-center {
  background-color: @background-color-light;
  border-radius: @border-radius-lg;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: @margin-md;
  padding: @padding-md;
  flex: 1;
  overflow: hidden;
  /* 让内容自然流动，但限制在容器内 */
}

.prison-select-center-top {
  padding: @padding-lg;
  width: 100%;
  border-radius: @border-radius-lg;
  overflow: hidden;
  background: @background-color-base;
  display: flex;
  box-shadow: @box-shadow-light;
  border: 1px solid @border-color-light;
  transition: all 0.3s ease;
  flex-shrink: 0; /* 防止被压缩 */

  &:hover {
    box-shadow: @box-shadow-medium;
  }
}

.prison-select-center-bottom {
  flex: 1; /* 占用剩余空间 */
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许内容溢出时显示滚动条 */

  .timeline-container {
    background: @background-color-base;
    width: 100%;
    height: 100%;
    border-radius: @border-radius-lg;
    box-shadow: @box-shadow-light;
    border: 1px solid @border-color-light;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;

    &:hover {
      box-shadow: @box-shadow-medium;
    }

    .sys-sub-title {
      background: linear-gradient(135deg, @primary-color 0%, @primary-color-hover 100%);
      color: @background-color-base;
      margin: 0;
      padding: @padding-md @padding-lg;
      font-size: @font-size-lg;
      font-weight: @font-weight-medium;
      border-bottom: 1px solid @border-color-light;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        left: @padding-lg;
        bottom: -1px;
        width: 60px;
        height: 3px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
      }
    }

    .timeline-wrapper {
      padding: @padding-lg;
      flex: 1;
      overflow-y: auto;

      /* 自定义滚动条样式 */
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: @screen-md) {
  .prison-select-center-top {
    flex-direction: column;
    gap: @margin-md;
  }

  .prison-select-center-top-ryxx,
  .prison-select-center-top-detail {
    width: 100%;
  }

  .prison-select-center-top-ryxx {
    margin-right: 0;
  }
}
</style>
