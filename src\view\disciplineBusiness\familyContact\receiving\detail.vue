<template>
  <!-- 详情页面 -->
  <div class="detail-wrap">
    <div class="bsp-base-form">
      <div class="bsp-base-tit">
        {{ editTitle }}
      </div>
      <div class="prison-select-center">
        <div class="prison-select-center-top">
          <div class="prison-select-center-top-ryxx">
            <personnel-selector
              :value="jgrybm"
              mode="detail"
              title="被监管人员"
              :show-case-info="true"
            />
          </div>
          <div class="prison-select-center-top-detail">
            <headerDetail :formData="formData" :jgryxm="jgryxm" :roomName="roomName"/>
          </div>
        </div>
        <div class="prison-select-center-bottom">
          <div class="timeline-container">
            <p class="sys-sub-title">流程轨迹</p>
            <div class="timeline-content">
              <record :formData="formData" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bsp-base-fotter">
      <Button style="margin: 0 20px" @click="handleClose()">返 回</Button>
    </div>
  </div>
</template>

<script>

import viewImg from "_c/main/components/viewImg/viewImg.vue"
import personnelSelector from "@/components/personnel-selector"
import headerDetail from "./headerDetail.vue"
import record from "./record.vue"
export default {
  props: {
    jgryxm: {
      default: '',
      type: String
    },
    jgrybm: {
      default: '',
      type: String
    },
    roomName: {
      default: '',
      type: String
    },
    curId: {
      default: '',
      type: String
    }
  },
  components: {headerDetail, viewImg, record, personnelSelector},
  data() {
    return {
      showattUrl: true,
      formData: {},
      defaultListsuper: [],
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName,
      editTitle: '收信信息'
    }
  },
  methods: {
    handleClose() {
      this.$emit('close', false)
    },
    getData() {
      this.$store.dispatch("authGetRequest", {
        url: this.$path.familyContact_get,
        params: {
          id: this.curId
        }
      }).then(res => {
        if (res.success) {
          this.formData = res.data
          this.formData.jgryxm = this.jgryxm
          this.formData.roomName = this.roomName
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '操作失败'
          })
        }
      })
    }
  },
  mounted() {
    this.getData()
  }
}
</script>
<style scoped lang="less">
@import '~@/assets/style/variables.less';

.prison-select-center-top-ryxx {
  width: 30%;
  margin-right: @margin-md;
}

.prison-select-center-top-detail {
  width: 70%;
}

.prison-select-center {
  background-color: @background-color-light;
  border-radius: @border-radius-lg;
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: @margin-md;
  padding: @padding-md;
}

.prison-select-center-top {
  padding: @padding-lg;
  width: 100%;
  border-radius: @border-radius-lg;
  overflow: hidden;
  background: @background-color-base;
  display: flex;
  box-shadow: @box-shadow-light;
  border: 1px solid @border-color-light;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: @box-shadow-medium;
  }
}

.prison-select-center-bottom {
  display: flex;
  flex: 1;
  min-height: 0;
  height: 100%;

  .timeline-container {
    background: @background-color-base;
    width: 100%;
    border-radius: @border-radius-lg;
    box-shadow: @box-shadow-light;
    border: 1px solid @border-color-light;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: @box-shadow-medium;
    }

    .sys-sub-title {
      background: linear-gradient(135deg, @primary-color 0%, @primary-color-hover 100%);
      color: @background-color-base;
      margin: 0;
      padding: @padding-md @padding-lg;
      font-size: @font-size-lg;
      font-weight: @font-weight-medium;
      border-bottom: 1px solid @border-color-light;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        left: @padding-lg;
        bottom: -1px;
        width: 60px;
        height: 3px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
      }
    }

    .timeline-content {
      padding: @padding-lg;
      max-height: calc(100vh - 400px);
      overflow-y: auto;

      /* 自定义滚动条样式 */
      &::-webkit-scrollbar {
        width: @scrollbar-width;
      }

      &::-webkit-scrollbar-track {
        background: @scrollbar-track-bg;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: @scrollbar-thumb-bg;
        border-radius: 3px;

        &:hover {
          background: @scrollbar-thumb-hover-bg;
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: @screen-md) {
  .prison-select-center-top {
    flex-direction: column;
    gap: @margin-md;
  }

  .prison-select-center-top-ryxx,
  .prison-select-center-top-detail {
    width: 100%;
  }

  .prison-select-center-top-ryxx {
    margin-right: 0;
  }
}
</style>
