<template>
  <div>
    <Card title="新增申诉" dis-hover :bordered="false">
 
        <div class="com-form-container">
          <div class="com-module-layout">
            <p class="detail-title">基本信息</p>
            <div class="com-content-wrapper">
              <com-grid-table :model="formItem" ref="formData"  :rules="formRules"  v-if="!formId">
                <com-grid-item label="申诉标题" prop="title" :col="3">
                  <ui-input  v-model="formItem.title"/>
                </com-grid-item>
                <com-grid-item label="申诉单位">
                  <com-form-text :value="formItem.unitName"></com-form-text>
                </com-grid-item>
                <com-grid-item label="申诉人">
                  <com-form-text :value="formItem.representUserName"></com-form-text>
                </com-grid-item>
                <com-grid-item label="申诉时间">
                  <com-form-text :value="formItem.representTime"></com-form-text>
                </com-grid-item>
                <com-grid-item label="申诉理由" prop="representReason" :col="3">
                  <ui-input  v-model="formItem.representReason" type="textarea" ></ui-input>
                </com-grid-item>
                <com-grid-item label="备注" :col="3">
                  <ui-input   v-model="formItem.remark" type="textarea" />
                </com-grid-item>
                <com-grid-item label="附件" :col="3">
                  <ui-multi-uploader v-model="formItem.files"></ui-multi-uploader>
                </com-grid-item>
              </com-grid-table>
              <com-grid-table :model="formItem" ref="formData" v-if="formId">
                <com-grid-item label="申诉标题" prop="title" :col="3">
                  <com-form-text :value="formItem.title"/>
                </com-grid-item>
                <com-grid-item label="申诉单位">
                  <com-form-text :value="formItem.unitName"></com-form-text>
                </com-grid-item>
                <com-grid-item label="申诉人">
                  <com-form-text :value="formItem.representUserName"></com-form-text>
                </com-grid-item>
                <com-grid-item label="申诉时间">
                  <com-form-text :value="formItem.representTime"></com-form-text>
                </com-grid-item>
                <com-grid-item label="申诉理由"  prop="representReason" :col="3">
                  <com-form-text  :value="formItem.representReason"></com-form-text>
                </com-grid-item>
                <com-grid-item label="备注" :col="3">
                  <com-form-text  :value="formItem.remark"></com-form-text>
                </com-grid-item>
                <com-grid-item label="附件" :col="3">
                  <ui-multi-uploader :value="formItem.files" readonly></ui-multi-uploader>
                </com-grid-item>
              </com-grid-table>
            </div>
            <com-hr/>
            <p  class="com-sub-title">关联督导单</p>
            <div class="com-table-wrapper">
              <relate-supervision-list  @on-update="updateTableData" source="prison" v-if="!basicEdit" :id="formId" type="1"></relate-supervision-list>
              <ui-table
                v-else
                @on-selection-change="selectTableData"
                :height="250"
                :data="tableData"
                :columns="tableHeaders"
              >
                <span slot-scope="{row}" class="com-table-btn" slot="operate" @click="changePage(row)">查看</span>
              </ui-table>
            </div>
          </div>
          <div class="com-form-submit" v-if="showEdit">
            <ui-button v-if="basicEdit"  type="gray" @click="handleBasicBack">取消</ui-button>
            <ui-button v-if="!basicEdit" type="white" wait="3000" @click="handleBasicEdit">编辑</ui-button>
            <ui-button v-if="basicEdit"  wait @click="handleBasicSave">保存</ui-button>
          </div>
          <div class="com-module-layout" v-if="formId">
            <template v-if="isLeader">
              <p  class="com-sub-title">领导意见</p>
              <div class="com-content-wrapper">
                <com-grid-table :model="leaderFormItem" ref="leaderForm" :rules="leaderFormRules" >
                  <com-grid-item label="意见" prop="leaderOpinion">
                    <ui-select v-model="leaderFormItem.leaderOpinion">
                      <Option value="1">同意</Option>
                      <Option value="0">不同意</Option>
                    </ui-select>
                  </com-grid-item>
                  <com-grid-item label="领导签名">
                    <com-form-text :value="leaderFormItem.leaderName"></com-form-text>
                  </com-grid-item>
                  <com-grid-item label="签名时间">
                    <com-form-text :value="leaderFormItem.leaderTime"></com-form-text>
                  </com-grid-item>
                  <com-grid-item label="备注" :col="3">
                    <ui-input v-model="leaderFormItem.leaderRemake"   type="textarea" />
                  </com-grid-item>
                  <com-grid-item label="附件" :col="3">
                    <ui-multi-uploader v-model="leaderFormItem.files"></ui-multi-uploader>
                  </com-grid-item>
                </com-grid-table>
              </div>
              <com-hr/>
            </template>
            <p  class="com-sub-title" >信息补充</p>
            <div class="com-content-wrapper">
              <com-grid-table :model="infoFormItem"  ref="infoForm" :rules="infoFormRules" >
                <com-grid-item label="信息补充" prop="supply" :col="3">
                  <ui-input v-model="infoFormItem.supply" type="textarea" />
                </com-grid-item>
                <com-grid-item label="附件"  :col="3">
                  <ui-multi-uploader v-model="infoFormItem.files"></ui-multi-uploader>
                </com-grid-item>
              </com-grid-table>
            </div>
          </div>
          <div class="com-form-submit">
            <!-- 暂时屏蔽入口，待业务重构完再处理 -->
            <!-- <ui-button  @click="forwardModal = true">转发</ui-button> -->
            <ui-button type="gray" @click="handleBack()">返回</ui-button>
            <ui-button type="white" @click="handleSave">保存</ui-button>
            <ui-button @click="handleSend">提交</ui-button>
          </div>
        </div>
        <div class="com-module-layout">
          <div class="com-content-wrapper">
            <transfer-record ref="transferRecord" source="prison" :id="formId" type="1" v-if="formId"/>
          </div>
        </div>
    </Card>
  </div>
</template>
<script>
import {keepRepresentForm, facingPage} from "@/axios/zhjgBranchWork";
import {fileTranslate, cloneDeep} from "@/util";
import { OPERTYPES } from "@/constant";
import {forwardForm, transferRecord, relateSupervisionList} from "../../components";
// 传参
// row: 详情信息
// tableData: 督导表格
export default {
  name: "prisonComplainForm",
  components: {
    forwardForm,
    transferRecord,
    relateSupervisionList
  },
  data() {
    const userInfo = this.$store.state.userInfo;
    const isLeader = this.$hasAuth("JSDD_SPSS");
    return {
      userInfo,
      judgeDate: new Date(new Date().setDate(new Date().getDate() - 3)),
      basicEdit: false,
      showEdit: false,
      formId: "",
      formItem: {
        files: [],
        representTime: new Date().Format("yyyy-MM-dd hh:mm:ss"),
        unitId: userInfo.prisonId,
        unitName: userInfo.prisonName,
        representUserId: userInfo.userid,
        representUserName: userInfo.name,
      },
      formItemShow: {},
      leaderFormItem: {
        files: [],
      },
      infoFormItem: {
        files: [],
      },
      formRules: {
        title: [
          {required: true, trigger: "change", message: "请填写申诉标题"},
        ],
        representReason: [
          {required: true, trigger: "change", message: "请填写申诉理由"},
        ],
      },
      leaderFormRules: {
        leaderOpinion: [{required: true, trigger: "change", message: "请选择领导意见"}]
      },
      infoFormRules: isLeader ?   {} : {
        supply: [{ required: true, trigger: "change",  message: "请填写信息补充" }]
      },
      tableHeaders: [
        {
          type: "selection",
          width: 80,
        },
        {
          title: "序号",
          type: "index",
          width: 80,
        },
        {
          title: "违规对象",
          key: "outlineObject",
        },

        {
          title: "违规类型",
          key: "questions",
        },
        {
          title: "违规时间",
          key: "outlineTime",
          width: 220,
        },
        {
          title: "发送时间",
          key: "forwardTime",
          width: 220,
        },
        {
          title: "登记人",
          key: "operateUserName",
        },
        {
          title: "登记时间",
          key: "operateTime",
          width: 220,
        },
        {
          title: "操作",
          slot: "operate",
          width: 100,
        },
      ],
      tableData: [],
      tableInitData: [],
      selectData: [],
      forwardModal: false,
      isLeader,
    };
  },
  async created() {
    let param =  this.$route.params || {};
    if (param.row) {
      this.getInfo(param.row);
      this.basicEdit = false;
      this.showEdit = `${this.userInfo.userid}` === `${param.row.representUserId}`;
    } else {
      this.basicEdit = true;
      this.selectData = param.tableData || [];
      this.tableData = await this.getTableData() || [];
      this.checkTableData();
    }
  },
  methods: {
    getInfo(data) {
      let info = {...data, files: fileTranslate(data.files)};
      this.formItem = cloneDeep(info);
      this.formItemShow = cloneDeep(info);
      this.formId = info.id;
      this.leaderFormItem = {
        leaderId: this.userInfo.userid,
        id: this.formId,
        leaderName: this.userInfo.name,
        leaderTime: new Date().Format("yyyy-MM-dd hh:mm:ss"),
        leaderOpinion: info.leaderOpinion ? `${info.leaderOpinion}` : this.isLeader ? "1" : "",
        leaderRemake: info.leaderRemake || "",
        files: fileTranslate(info.files1),
      };
      this.infoFormItem = {
        id: this.formId,
        supply: info.informationSupplement || "",
        files: fileTranslate(info.files2),
      };
    },
    changePage(row) {
      this.$toPage({
        name: "prisonSupervisionRecordDetail",
        params: {id: row.id, source: "prison"}
      });
    },
    async validateForm() {
      if (!this.formId) {
        return await this.$refs.formData.validateFromIndex();
      } else {
        return  (!this.isLeader || (this.isLeader &&  await this.$refs.leaderForm.validateFromIndex()))
            && await this.$refs.infoForm.validateFromIndex();
      }
    },
    validateTime() {
      for (let i = 0; i < this.selectData.length; i++) {
        let time = this.selectData[i].forwardTime;
        if (time && new Date(time) < this.judgeDate) {
          this.$messageInfo("发送到所里含有超过三天的督导，不允许进行操作");
          return false;
        }
      }
      return true;
    },
    // 所内 0:保存 1：转发 2:发送   支队 2：保存 3：转发 4：完成
    async submitBasic(code, param = {}, back = true) {
      if (this.selectData && this.selectData.length === 0 && !this.formId) {
        this.$messageInfo("请至少选择一条关联督导单");
        return false;
      }
      let basicForm = {
        ...this.formItem,
        leaderDto: {...this.leaderFormItem, files: fileTranslate(this.leaderFormItem.files, true), type: "1"},
        supplyDto: {...this.infoFormItem, files: fileTranslate(this.infoFormItem.files, true),  type: "1"},
        branchSuperviseId: this.selectData.map(item => item.id).join(","),
        files: fileTranslate(this.formItem.files, true),
        operateTime: new Date().Format("yyyy-MM-dd hh:mm:ss"),
        operateCode: code,
        type: "1", // 0:支队 1:所内
        id: this.formId,
      };
      let submitParam = Object.assign({}, basicForm, param);
      let type = this.formId ? OPERTYPES.modify : OPERTYPES.add;
      return keepRepresentForm(submitParam, type).then(() => {
        this.$operateSuccessMessage();
        back && this.handleBack(true);
        return true;
      }).catch(err => {
        this.$messageError(err);
        return false;
      });
    },
    async handleSave() {
      if (!this.validateTime()) return;
      if (!await this.validateForm()) return  this.$invalidMessage();
      let code = this.formId  ? "1" : "0";
      await this.submitBasic(code, {status: 0});
    },
    async handleForward(data) {
      if (!await this.validateForm()) return  this.$invalidMessage();
      let param = {
        forwardSuperviseDto: data,
        status: 0
      };
      let result  = await this.submitBasic("2", param, false);
      if (result) {
        this.forwardModal = false;
        this.handleBack(true);
      }
    },
    async handleSend() {
      if (!await this.validateForm()) return  this.$invalidMessage();
      let param = {
        superviseProcessEntity: {
          id: this.formId,
          userId: this.userInfo.userid,
          userName: this.userInfo.name,
          processType: "1", // 流程类型（0：支队，1：监所)
        },
      };
      await this.submitBasic("3", param);
    },
    handleBack(flag) {
      this.$toPage(-1, flag ? 1 : 0);
    },
    async handleBasicSave() {
      if (!this.validateTime()) return;
      let result = await this.submitBasic("4", {status: 0}, false);
      if (result) {
        this.$refs.transferRecord.getDetail(this.formId);
        this.formItemShow = cloneDeep(this.formItem);
        this.basicEdit = false;
      }
    },
    async handleBasicEdit() {
      this.basicEdit = true;
      this.$nextTick(() => {
        this.$set(this.formItem, "representTime", new Date().Format("yyyy-MM-dd hh:mm:ss"));
      });
      let data = await this.getTableData() || [];
      this.tableData = [].concat(this.tableInitData).concat(data);
      this.checkTableData();
    },
    handleBasicBack() {
      this.basicEdit = false;
      this.formItem = cloneDeep(this.formItemShow);
    },
    updateTableData(data) {
      this.tableInitData = data;
      this.selectData = data;
    },
    selectTableData(data) {
      this.selectData = data;
    },
    checkTableData() {
      this.tableData.forEach((item, idx) => {
        let obj = this.selectData.find((data) => data.id === item.id);
        this.$set(this.tableData[idx], "_checked", Boolean(obj));
      });
    },
    getTableData() {
      let obj = {
        unitId: this.userInfo.prisonId,
        unitName: this.userInfo.prisonName,
        status: "1",
        representStatus: "0",
        curPage: 1,
        pageSize: 9999,
      };
      return facingPage(obj).then((res) => res.data.rows || []).catch(err => {
        this.$messageError(err);
      });
    },
  },
};
</script>
