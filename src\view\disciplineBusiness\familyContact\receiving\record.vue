<template>
  <div class="record-container">
    <!-- 时间轴区域 -->
    <div class="timeline">
        <!--收信登记-->
        <div class="timeline-item success">
          <div class="timeline-card">
            <div class="timeline-header">
              <h4 class="timeline-title">
                <i class="timeline-icon">📝</i>
                收信登记
              </h4>
              <span class="timeline-badge success">已完成</span>
            </div>
            <div class="timeline-content">
              <p class="timeline-info"><span class="label">收信人：</span>{{ formData.jgryxm }}（{{ formData.roomName }}）</p>
              <p class="timeline-time">{{ formData.registerTime }}</p>
            </div>
          </div>
        </div>

        <!-- 管教审核 -->
        <div v-if="formData.status === '01' || formData.gjApprovalResult" :class="['timeline-item', formData.status === '01'? 'pending': formData.gjApprovalResult === '5'? 'success':'failed pulse']">
          <div class="timeline-card">
            <div class="timeline-header">
              <h4 class="timeline-title">
                <i class="timeline-icon">👨‍💼</i>
                管教审核
              </h4>
              <span :class="['timeline-badge', formData.gjApprovalResult? formData.gjApprovalResult === '5'? 'success' : 'failed' :'pending']">
                {{ formData.gjApprovalResult ? formData.gjApprovalResult === '5' ? '通过' : '另行处理' : formData.statusName }}
              </span>
            </div>
            <div class="timeline-content">
              <p class="timeline-info" v-if="formData.gjApprovalComments">
                <span class="label">{{ formData.gjApprovalResult === '2' ? '另行处理情况：' : '审核意见：' }}</span>
                {{ formData.gjApprovalComments }}
              </p>
              <p class="timeline-info" v-if="formData.gjApproverXm">
                <span class="label">审核人：</span>{{ formData.gjApproverXm }}
              </p>
              <p class="timeline-time" v-if="formData.gjApproverTime">{{ formData.gjApproverTime }}</p>
            </div>
          </div>
        </div>

        <!-- 科组长审核 -->
        <div v-if="formData.status === '02' || formData.groupApprovalResult" :class="['timeline-item', formData.status === '02'? 'pending': formData.groupApprovalResult === '5'? 'success':'failed pulse']">
          <div class="timeline-card">
            <div class="timeline-header">
              <h4 class="timeline-title">
                <i class="timeline-icon">👔</i>
                科组长审核
              </h4>
              <span :class="['timeline-badge', formData.groupApprovalResult? formData.groupApprovalResult === '5'? 'success' : 'failed' :'pending']">
                {{ formData.groupApprovalResult ? formData.groupApprovalResult === '5' ? '通过' : '不通过' : formData.statusName }}
              </span>
            </div>
            <div class="timeline-content">
              <p class="timeline-info" v-if="formData.groupApprovalComments">
                <span class="label">审核意见：</span>{{ formData.groupApprovalComments }}
              </p>
              <p class="timeline-info" v-if="formData.groupApproverXm">
                <span class="label">审核人：</span>{{ formData.groupApproverXm }}
              </p>
              <p class="timeline-time" v-if="formData.groupApproverTime">{{ formData.groupApproverTime }}</p>
            </div>
          </div>
        </div>

        <!-- 所领导审核 -->
        <div v-if="formData.status === '03' || formData.leaderApprovalResult" :class="['timeline-item', formData.status === '03'? 'pending': formData.leaderApprovalResult === '5'? 'success':'failed pulse']">
          <div class="timeline-card">
            <div class="timeline-header">
              <h4 class="timeline-title">
                <i class="timeline-icon">🏢</i>
                所领导审核
              </h4>
              <span :class="['timeline-badge', formData.leaderApprovalResult? formData.leaderApprovalResult === '5'? 'success' : 'failed' :'pending']">
                {{ formData.leaderApprovalResult ? formData.leaderApprovalResult === '5' ? '通过' : '不通过' : formData.statusName }}
              </span>
            </div>
            <div class="timeline-content">
              <p class="timeline-info" v-if="formData.leaderApprovalComments">
                <span class="label">审核意见：</span>{{ formData.leaderApprovalComments }}
              </p>
              <p class="timeline-info" v-if="formData.leaderApproverXm">
                <span class="label">审核人：</span>{{ formData.leaderApproverXm }}
              </p>
              <p class="timeline-time" v-if="formData.leaderApproverTime">{{ formData.leaderApproverTime }}</p>
            </div>
          </div>
        </div>

        <!-- 信件转交 -->
        <div v-if="formData.status === '04' || formData.passUser" :class="['timeline-item', formData.status === '04'? 'pending': formData.passUser? 'success':'failed pulse']">
          <div class="timeline-card">
            <div class="timeline-header">
              <h4 class="timeline-title">
                <i class="timeline-icon">📤</i>
                信件转交
              </h4>
              <span :class="['timeline-badge', formData.passUser? 'success' :'pending']">
                {{ formData.passUser? '转交' : formData.statusName }}
              </span>
            </div>
            <div class="timeline-content">
              <p class="timeline-info" v-if="formData.passRemark">
                <span class="label">备注：</span>{{ formData.passRemark }}
              </p>
              <p class="timeline-info" v-if="formData.passUser">
                <span class="label">转交人：</span>{{ formData.passUser }}
              </p>
              <p class="timeline-time" v-if="formData.passTime">{{ formData.passTime }}</p>
            </div>
          </div>
        </div>

        <!-- 收信确认 -->
        <div v-if="formData.status === '05' || formData.receiptTime" :class="['timeline-item', formData.status === '05'? 'pending': formData.receiptTime? 'success':'failed pulse']">
          <div class="timeline-card">
            <div class="timeline-header">
              <h4 class="timeline-title">
                <i class="timeline-icon">✅</i>
                收信确认
              </h4>
              <span :class="['timeline-badge', formData.receiptTime? 'success' :'pending']">
                {{ formData.receiptTime ? '已确认' : formData.statusName }}
              </span>
            </div>
            <div class="timeline-content">
              <p class="timeline-info" v-if="formData.passUser">
                <span class="label">收信签名：</span>{{ formData.passUser }}
              </p>
              <p class="timeline-time" v-if="formData.receiptTime">{{ formData.receiptTime }}</p>
            </div>
          </div>
        </div>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    formData: Object
  },
  name: 'record',
  data() {
    return {}
  },
  methods: {}
}
</script>
<style lang="less">
@import '~@/assets/style/variables.less';

.record-container {
  margin: 0;
  padding: 0;
  font-family: 'Microsoft YaHei', 'Helvetica Neue', Helvetica, sans-serif;
  color: @text-color-primary;
  width: 100%;
  min-height: 200px; /* 确保容器有最小高度 */
}

/* 颜色定义 */
.primary {
  color: @primary-color;
}

.success {
  color: @success-color;
}

.danger {
  color: @error-color;
}

.neutral {
  color: @background-color-gray;
}

.neutral-dark {
  color: @text-color-placeholder;
}

/* 紧凑型卡片样式 */
.timeline-card {
  background-color: @background-color-base;
  border-radius: @border-radius-sm;
  box-shadow: @box-shadow-light;
  border: 1px solid @border-color-light;
  margin-bottom: 8px;
  transition: all 0.3s ease;
  overflow: hidden;
  font-size: 11px;
}

.timeline-card:hover {
  box-shadow: @box-shadow-medium;
  transform: translateY(-1px);
}

/* 标题样式 */
.title {
  font-size: 20px;
  font-weight: bold;
  margin: 0;
  display: flex;
  align-items: center;
}

.title i {
  margin-right: 10px;
}

/* 紧凑型时间轴样式 */
.timeline {
  position: relative;
  margin-left: 20px;
  padding: 10px 0;
}

.timeline::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 0;
  bottom: 0;
  width: 2px;
  border-radius: 1px;
  background: linear-gradient(to bottom, #e9ecef, #2b5fd9, #e9ecef);
}

.timeline-item {
  position: relative;
  margin-bottom: 12px;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -18px;
  top: 6px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid;
  background-color: white;
  z-index: 10;
  font-size: 12px;
  font-weight: bold;
}

.timeline-item.failed::before {
  content: '×';
  border-color: #e60012;
  color: #e60012;
}

.timeline-item.success::before {
  content: '✓';
  border-color: #11c28a;
  color: #11c28a;
}

.timeline-item.pending::before {
  content: '•';
  border-color: #999999;
  color: #999999;
}

.timeline-item.failed.pulse::before {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(230, 0, 18, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(230, 0, 18, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(230, 0, 18, 0);
  }
}

/* 徽章样式 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: @padding-xs @padding-sm;
  border-radius: @border-radius-xl;
  font-size: @font-size-xs;
  font-weight: @font-weight-medium;
  transition: all 0.3s ease;
  box-shadow: @box-shadow-light;
}

.badge i {
  margin-right: @margin-xs;
}

.badge-success {
  background: linear-gradient(135deg, rgba(17, 194, 138, 0.1), rgba(17, 194, 138, 0.2));
  color: @success-color;
  border: 1px solid rgba(17, 194, 138, 0.3);
}

.badge-success:hover {
  background: linear-gradient(135deg, rgba(17, 194, 138, 0.2), rgba(17, 194, 138, 0.3));
  transform: translateY(-1px);
}

.badge-failed {
  background: linear-gradient(135deg, rgba(230, 0, 18, 0.1), rgba(230, 0, 18, 0.2));
  color: @error-color;
  border: 1px solid rgba(230, 0, 18, 0.3);
}

.badge-failed:hover {
  background: linear-gradient(135deg, rgba(230, 0, 18, 0.2), rgba(230, 0, 18, 0.3));
  transform: translateY(-1px);
}

.badge-pending {
  background: linear-gradient(135deg, rgba(153, 153, 153, 0.1), rgba(153, 153, 153, 0.2));
  color: @text-color-placeholder;
  border: 1px solid rgba(153, 153, 153, 0.3);
}

.badge-pending:hover {
  background: linear-gradient(135deg, rgba(153, 153, 153, 0.2), rgba(153, 153, 153, 0.3));
  transform: translateY(-1px);
}

/* 紧凑型时间轴内容样式 */
.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: @padding-sm @padding-md;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid @border-color-light;
}

.timeline-title {
  font-size: @font-size-sm;
  font-weight: @font-weight-medium;
  margin: 0;
  display: flex;
  align-items: center;
  color: @text-color-primary;

  .timeline-icon {
    margin-right: @margin-xs;
    font-size: @font-size-md;
  }
}

.timeline-badge {
  padding: 2px 8px;
  border-radius: @border-radius-sm;
  font-size: 11px;
  font-weight: @font-weight-medium;

  &.success {
    background: rgba(17, 194, 138, 0.1);
    color: #11c28a;
    border: 1px solid rgba(17, 194, 138, 0.3);
  }

  &.failed {
    background: rgba(230, 0, 18, 0.1);
    color: #e60012;
    border: 1px solid rgba(230, 0, 18, 0.3);
  }

  &.pending {
    background: rgba(153, 153, 153, 0.1);
    color: #999999;
    border: 1px solid rgba(153, 153, 153, 0.3);
  }
}

.timeline-content {
  padding: @padding-md;
}

.timeline-info {
  margin: @padding-xs 0;
  font-size: @font-size-xs;
  line-height: 1.4;

  .label {
    color: @text-color-secondary;
    font-weight: @font-weight-medium;
    margin-right: @margin-xs;

    &::after {
      content: '';
    }
  }
}

.timeline-time {
  margin: @padding-sm 0 0 0;
  font-size: 11px;
  color: @text-color-placeholder;
  text-align: right;
  font-style: italic;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding-top: @padding-xs;
}

/* 按钮样式 */
.buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.btn {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 8px;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-family: inherit;
  font-size: 14px;
}

.btn i {
  margin-right: 5px;
}

.btn-gray {
  background-color: #F3F4F6;
  color: #4B5563;
}

.btn-gray:hover {
  background-color: #E5E7EB;
}

.btn-primary {
  background-color: #165DFF;
  color: white;
}

.btn-primary:hover {
  background-color: #0B4CDB;
}
</style>
