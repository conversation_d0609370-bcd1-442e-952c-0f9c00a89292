<template>
  <div class="record-container">
    <!-- 时间轴区域 -->
    <div class="timeline">
        <!--收信登记-->
        <div class="timeline-item success">
          <div class="card">
            <div class="timeline-header">
              <h3 class="timeline-title">
                <i class="timeline-icon">📝</i>
                收信登记
              </h3>
            </div>
            <div class="timeline-content">
              <div>
                <p><span class="font-medium">收信人：</span>{{ formData.jgryxm }}（{{ formData.roomName }}）</p>
              </div>
              <div>
                <p class="text-sm neutral-dark"><span class="font-medium">登记时间：</span>{{
                    formData.registerTime
                  }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 管教审核 -->
        <div v-if="formData.status === '01' || formData.gjApprovalResult" :class="['timeline-item', formData.status === '01'? 'pending': formData.gjApprovalResult === '5'? 'success':'failed pulse']">
          <div class="card">
            <div class="timeline-header">
              <h3 class="timeline-title">
                <i class="timeline-icon">👨‍💼</i>
                管教审核
              </h3>
              <span :class="['badge', formData.gjApprovalResult? formData.gjApprovalResult === '5'? 'badge-success' : 'badge-failed' :'badge-pending']">
                <i class="badge-icon">•</i> {{
                  formData.gjApprovalResult ? formData.gjApprovalResult === '5' ? '通过' : '另行处理' : formData.statusName
                }}
              </span>
            </div>
            <div class="timeline-content">
              <div v-if="formData.gjApprovalResult === '2'">
                <p>
                  <span class="font-medium">另行处理情况：</span>
                  {{ formData.gjApprovalComments }}
                </p>
              </div>
              <div v-if="formData.gjApprovalResult !== '2'">
                <p><span class="font-medium">审核意见：</span>{{ formData.gjApprovalComments }}</p>
              </div>
              <div>
                <p><span class="font-medium">审核人：</span>{{ formData.gjApproverXm }}</p>
                <p class="text-sm neutral-dark"><span class="font-medium">审核时间：</span>{{
                    formData.gjApproverTime
                  }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 科组长审核 -->
        <div v-if="formData.status === '02' || formData.groupApprovalResult" :class="['timeline-item', formData.status === '02'? 'pending': formData.groupApprovalResult === '5'? 'success':'failed pulse']">
          <div class="card">
            <div class="timeline-header">
              <h3 class="timeline-title">
                <i class="timeline-icon">👔</i>
                科组长审核
              </h3>
              <span :class="['badge', formData.groupApprovalResult? formData.groupApprovalResult === '5'? 'badge-success' : 'badge-failed' :'badge-pending']">
                <i class="badge-icon">•</i> {{
                  formData.groupApprovalResult ? formData.groupApprovalResult === '5' ? '通过' : '不通过' : formData.statusName
                }}
              </span>
            </div>
            <div class="timeline-content">
              <div>
                <p><span class="font-medium">审核意见：</span>{{ formData.groupApprovalComments }}</p>
              </div>
              <div>
                <p><span class="font-medium">审核人：</span>{{ formData.groupApproverXm }}</p>
                <p class="text-sm neutral-dark"><span class="font-medium">审核时间：</span>{{
                    formData.groupApproverTime
                  }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 所领导审核 -->
        <div v-if="formData.status === '03' || formData.leaderApprovalResult" :class="['timeline-item', formData.status === '03'? 'pending': formData.leaderApprovalResult === '5'? 'success':'failed pulse']">
          <div class="card">
            <div class="timeline-header">
              <h3 class="timeline-title">
                <i class="timeline-icon">🏢</i>
                所领导审核
              </h3>
              <span :class="['badge', formData.leaderApprovalResult? formData.leaderApprovalResult === '5'? 'badge-success' : 'badge-failed' :'badge-pending']">
                <i class="badge-icon">•</i> {{
                  formData.leaderApprovalResult ? formData.leaderApprovalResult === '5' ? '通过' : '不通过' : formData.statusName
                }}
              </span>
            </div>
            <div class="timeline-content">
              <div>
                <p><span class="font-medium">审核意见：</span>{{ formData.leaderApprovalComments }}</p>
              </div>
              <div>
                <p><span class="font-medium">审核人：</span>{{ formData.leaderApproverXm }}</p>
                <p class="text-sm neutral-dark"><span class="font-medium">审核时间：</span>{{
                    formData.leaderApproverTime
                  }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 信件转交 -->
        <div v-if="formData.status === '04' || formData.passUser" :class="['timeline-item', formData.status === '04'? 'pending': formData.passUser? 'success':'failed pulse']">
          <div class="card">
            <div class="timeline-header">
              <h3 class="timeline-title">
                <i class="timeline-icon">📤</i>
                信件转交
              </h3>
              <span :class="['badge', formData.passUser? 'badge-success' :'badge-pending']">
                <i class="badge-icon">•</i> {{
                  formData.passUser? '转交' : formData.statusName
                }}
              </span>
            </div>
            <div class="timeline-content">
              <div>
                <p><span class="font-medium">备注：</span>{{ formData.passRemark }}</p>
              </div>
              <div>
                <p><span class="font-medium">转交人：</span>{{ formData.passUser }}</p>
                <p class="text-sm neutral-dark"><span class="font-medium">转交时间：</span>{{ formData.passTime }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 收信确认 -->
        <div v-if="formData.status === '05' || formData.receiptTime" :class="['timeline-item', formData.status === '05'? 'pending': formData.receiptTime? 'success':'failed pulse']">
          <div class="card">
            <div class="timeline-header">
              <h3 class="timeline-title">
                <i class="fa fa-envelope-open-o"></i>
                收信确认
              </h3>
              <span class="badge badge-pending">
                <i class="fa fa-clock-o"></i> {{ formData.statusName }}
              </span>
            </div>
            <div class="timeline-content">
              <div>
                <p><span class="font-medium">收信签名：</span>{{ formData.passUser }}</p>
                <p class="text-sm neutral-dark"><span class="font-medium">签收时间：</span>{{ formData.receiptTime }}</p>
              </div>
            </div>
          </div>
        </div>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    formData: Object
  },
  name: 'record',
  data() {
    return {}
  },
  methods: {}
}
</script>
<style lang="less">
@import '~@/assets/style/variables.less';

.record-container {
  margin: 0;
  padding: 0;
  font-family: 'Microsoft YaHei', 'Helvetica Neue', Helvetica, sans-serif;
  color: @text-color-primary;
  width: 100%;
  min-height: 200px; /* 确保容器有最小高度 */
}

/* 颜色定义 */
.primary {
  color: @primary-color;
}

.success {
  color: @success-color;
}

.danger {
  color: @error-color;
}

.neutral {
  color: @background-color-gray;
}

.neutral-dark {
  color: @text-color-placeholder;
}

/* 卡片样式 */
.card {
  background-color: @background-color-base;
  border-radius: @border-radius-lg;
  box-shadow: @box-shadow-light;
  border: 1px solid @border-color-light;
  padding: @padding-lg;
  margin-bottom: @margin-md;
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: @box-shadow-medium;
  transform: translateY(-2px);
}

/* 标题样式 */
.title {
  font-size: 20px;
  font-weight: bold;
  margin: 0;
  display: flex;
  align-items: center;
}

.title i {
  margin-right: 10px;
}

/* 时间轴样式 */
.timeline {
  position: relative;
  margin-left: 40px;
  padding: 20px 0;
}

.timeline::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 0;
  bottom: 0;
  width: 4px;
  border-radius: 2px;
  background: #2b5fd9;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -38px;
  top: 8px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid;
  background-color: white;
  z-index: 10;
  font-size: 16px;
  font-weight: bold;
}

.timeline-item.failed::before {
  content: '×';
  border-color: #e60012;
  color: #e60012;
}

.timeline-item.success::before {
  content: '✓';
  border-color: #11c28a;
  color: #11c28a;
}

.timeline-item.pending::before {
  content: '•';
  border-color: #999999;
  color: #999999;
}

.timeline-item.failed.pulse::before {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(230, 0, 18, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(230, 0, 18, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(230, 0, 18, 0);
  }
}

/* 徽章样式 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: @padding-xs @padding-sm;
  border-radius: @border-radius-xl;
  font-size: @font-size-xs;
  font-weight: @font-weight-medium;
  transition: all 0.3s ease;
  box-shadow: @box-shadow-light;
}

.badge i {
  margin-right: @margin-xs;
}

.badge-success {
  background: linear-gradient(135deg, rgba(17, 194, 138, 0.1), rgba(17, 194, 138, 0.2));
  color: @success-color;
  border: 1px solid rgba(17, 194, 138, 0.3);
}

.badge-success:hover {
  background: linear-gradient(135deg, rgba(17, 194, 138, 0.2), rgba(17, 194, 138, 0.3));
  transform: translateY(-1px);
}

.badge-failed {
  background: linear-gradient(135deg, rgba(230, 0, 18, 0.1), rgba(230, 0, 18, 0.2));
  color: @error-color;
  border: 1px solid rgba(230, 0, 18, 0.3);
}

.badge-failed:hover {
  background: linear-gradient(135deg, rgba(230, 0, 18, 0.2), rgba(230, 0, 18, 0.3));
  transform: translateY(-1px);
}

.badge-pending {
  background: linear-gradient(135deg, rgba(153, 153, 153, 0.1), rgba(153, 153, 153, 0.2));
  color: @text-color-placeholder;
  border: 1px solid rgba(153, 153, 153, 0.3);
}

.badge-pending:hover {
  background: linear-gradient(135deg, rgba(153, 153, 153, 0.2), rgba(153, 153, 153, 0.3));
  transform: translateY(-1px);
}

/* 内容样式 */
.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: @margin-md;
  padding-bottom: @padding-sm;
  border-bottom: 1px solid @border-color-light;
}

.timeline-title {
  font-size: @font-size-lg;
  font-weight: @font-weight-medium;
  margin: 0;
  display: flex;
  align-items: center;
  color: @text-color-primary;
}

.timeline-title i {
  margin-right: @margin-sm;
  color: @primary-color;
  font-size: @font-size-md;
}

.timeline-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: @margin-sm;
  padding-top: @padding-sm;
}

@media (min-width: @screen-sm) {
  .timeline-content {
    grid-template-columns: 1fr 1fr;
  }
}

.timeline-content p {
  margin: @margin-xs 0;
  line-height: 1.6;
}

.font-medium {
  font-weight: @font-weight-medium;
  color: @text-color-primary;
}

.text-sm {
  font-size: @font-size-xs;
  color: @text-color-placeholder;
}

/* 按钮样式 */
.buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.btn {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 8px;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-family: inherit;
  font-size: 14px;
}

.btn i {
  margin-right: 5px;
}

.btn-gray {
  background-color: #F3F4F6;
  color: #4B5563;
}

.btn-gray:hover {
  background-color: #E5E7EB;
}

.btn-primary {
  background-color: #165DFF;
  color: white;
}

.btn-primary:hover {
  background-color: #0B4CDB;
}
</style>
